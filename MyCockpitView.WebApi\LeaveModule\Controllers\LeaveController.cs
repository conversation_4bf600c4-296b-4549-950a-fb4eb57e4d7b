﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.LeaveModule.Services;
using MyCockpitView.WebApi.LeaveModule.Dtos;
using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.LeaveModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class LeaveController : ControllerBase
{
    private readonly ILeaveService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly ICurrentUserService currentUserService;

    public LeaveController(EntitiesContext db, ILeaveService service, IMapper mapper, IActivityService activityService, IContactService contactService, ICurrentUserService currentUserService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.currentUserService = currentUserService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<LeaveDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);
        var results = mapper.Map<IEnumerable<LeaveDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Leave))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Leave))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<LeaveDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<LeaveDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Leave))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Leave))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<LeaveDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<LeaveDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<LeaveDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Leave))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Leave))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<LeaveDto>> Post(LeaveDto dto)
    {
          var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        await service.ValidateApplication(mapper.Map<Leave>(dto), currentContact != null && dto.ContactID == currentContact.ID);

        var id = await service.Create(mapper.Map<Leave>(dto));
        var responseDto = mapper.Map<LeaveDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Leave))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Leave))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

               
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Leave), responseDto.ID, $"{responseDto.Type}", $"{responseDto.Start.ToString("dd MMM yyyy")} - {responseDto.End.ToString("dd MMM yyyy")} | {responseDto.Total} Days ", "Created", responseDto.Reason);
        }

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<LeaveDto>> Put(int id, LeaveDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Leave>(dto));
        var responseDto = mapper.Map<LeaveDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Leave))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Leave))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Leave), responseDto.ID, $"{responseDto.Type}", $"{responseDto.Start.ToString("dd MMM yyyy")} - {responseDto.End.ToString("dd MMM yyyy")} | {responseDto.Total} Days ", "Updated", responseDto.Reason);
        }


        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<LeaveDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Leave)} not found!");

        await service.Delete(id);

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Leave), responseDto.ID, $"{responseDto.Type}", $"{responseDto.Start.ToString("dd MMM yyyy")} - {responseDto.End.ToString("dd MMM yyyy")} | {responseDto.Total} Days ", "Deleted" , responseDto.Reason);
        }

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<LeaveDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<LeaveDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Leave))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Leave))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }
    [HttpGet("Count")]
    public async Task<IActionResult> GetCount(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {

        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        return Ok(await query.CountAsync());

    }
    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }

    [HttpGet("MonthSummary/{id}")]
    public async Task<IActionResult> GetMonthSummary(int id, int year, int month)
    {

        var query = await service.GetMonthSummary(id, year, month);

        return Ok(query);

    }

    [HttpGet("PerMonthSummary/{id}")]
    public async Task<IActionResult> GetPerMonthSummary(int id, int index = 0)
    {

        var query = await service.GetPerMonthSummary(id, index);

        return Ok(query);

    }

    [HttpGet("TotalSummary/{id}")]
    public async Task<IActionResult> GetTotalSummary(int id, int index = 0)
    {

        var query = await service.GetTotalSummary(id, index);

        return Ok(query);

    }

    [HttpPost("Validate")]
    public async Task<IActionResult> Validate([FromBody] LeaveDto Dto)
    {

         var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);

        await service.ValidateApplication(mapper.Map<Leave>(Dto), currentContact != null && Dto.ContactID == currentContact.ID);

        return Ok();

    }

    [HttpPost("Split")]
    public async Task<IActionResult> Split([FromBody] LeaveDto Dto)
    {

        var result = await service.GetSplitLeave(mapper.Map<Leave>(Dto));

        return Ok(mapper.Map<IEnumerable<LeaveDto>>(result));

    }

}