﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.ExpenseModule.Services;
using MyCockpitView.WebApi.ExpenseModule.Dtos;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using System.Net.Mime;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ExpenseModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ExpenseController : ControllerBase
{
    private readonly IExpenseService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IWFTaskService taskService;
    private readonly ICurrentUserService currentUserService;

    public ExpenseController(EntitiesContext db, IExpenseService service, IMapper mapper, IActivityService activityService, IContactService contactService, IWFTaskService taskService, ICurrentUserService currentUserService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.taskService = taskService;
        this.currentUserService = currentUserService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ExpenseDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Attachments)
                    .Include(x => x.ApprovedByContact);
        var results = mapper.Map<IEnumerable<ExpenseDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Expense))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Expense))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ExpenseDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Attachments)
                    .Include(x => x.ApprovedByContact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ExpenseDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Expense))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Expense))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<ExpenseDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ExpenseDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ExpenseDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Expense))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Expense))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ExpenseDto>> Post(ExpenseDto dto)
    {
        var id = await service.Create(mapper.Map<Expense>(dto));
        var responseDto = mapper.Map<ExpenseDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

            if (responseDto.StatusFlag == McvConstant.EXPENSE_STATUS_FLAG_PENDING)
            {
                await taskService.StartFlow(nameof(Expense), responseDto.TypeFlag, responseDto.ID);
            }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Expense))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Expense))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Expense), responseDto.ID, $"{responseDto.ExpenseHead} | {responseDto.Code}",
                               "Date: " + ClockTools.GetIST(responseDto.ExpenseDate).ToString("dd MMM yyyy")
                   + "| Amount: " + DataTools.CurrencyFormat(responseDto.AmountDr), "Created");
        }

        if (responseDto.StatusFlag == McvConstant.EXPENSE_STATUS_FLAG_PENDING)
            await taskService.StartFlow(nameof(Expense), responseDto.TypeFlag, responseDto.ID);

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ExpenseDto>> Put(int id, ExpenseDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Expense>(dto));
        var responseDto = mapper.Map<ExpenseDto>(await service.GetById(id));

        if (responseDto.StatusFlag == McvConstant.EXPENSE_STATUS_FLAG_PENDING)
            await taskService.UpdateTaskDue(nameof(Expense), responseDto.ID);


        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Expense))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Expense))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {

                await activityService.LogUserActivity(currentContact, nameof(Expense), responseDto.ID, $"{responseDto.ExpenseHead} | {responseDto.Code}",
                               "Date: " + ClockTools.GetIST(responseDto.ExpenseDate).ToString("dd MMM yyyy")
                   + "| Amount: " + DataTools.CurrencyFormat(responseDto.AmountDr), "Updated");

            }
        }

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ExpenseDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Expense)} not found!");

        await service.Delete(id);

        await taskService.PurgePendingTasks(nameof(Expense), id);

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Expense), responseDto.ID, $"{responseDto.ExpenseHead} | {responseDto.Code}",
                               "Date: " + ClockTools.GetIST(responseDto.ExpenseDate).ToString("dd MMM yyyy")
                   + "| Amount: " + DataTools.CurrencyFormat(responseDto.AmountDr), "Deleted");
        }


        await taskService.PurgePendingTasks(nameof(Expense), id);
        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ExpenseDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ExpenseDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Expense))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Expense))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("startflow/{id}")]
    public async Task<IActionResult> StartFlow(int ID)
    {

        var Entity = await service.GetById(ID);
        if (Entity == null) return NotFound();

        await taskService.StartFlow(nameof(Expense), Entity.TypeFlag, Entity.ID);
        return Ok();

    }

    [HttpGet("SubjectOptions")]
    public async Task<ActionResult<IEnumerable<string>>> GetSubjectOptions()
    {
        var query = service.Get()
            .Where(x => x.Entity == null)
            .Select(x => x.Particulars)
            .Distinct();

        return Ok(await query.ToListAsync());
    }

    [HttpGet("Analysis/{dataType}")]
    public async Task<ActionResult<IEnumerable<Expense>>> GetVHrAnalysis(
        string dataType,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        if (!dataType.Equals("full", StringComparison.OrdinalIgnoreCase))
        {
            return BadRequest("Invalid data type specified");
        }

        var result = await service.Get(
            filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort
        ).ToListAsync();

        return Ok(result);
    }

    [AllowAnonymous]
    [HttpGet("Analysis/{dataType}/excel")]
    public async Task<IActionResult> GetVHrAnalysisExcel(
        string dataType,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        if (!dataType.Equals("full", StringComparison.OrdinalIgnoreCase))
        {
            return BadRequest("Invalid data type specified");
        }

        var report = await service.GetAnalysisExcel(
            filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort
        );

        if (report == null)
        {
            return BadRequest("Report cannot be generated");
        }

        var filename = $"Analysis-{DateTimeOffset.Now:dd-MMM-yyyy}.xlsx";

        return File(
            fileContents: report,
            contentType: MediaTypeNames.Application.Octet,
            fileDownloadName: filename,
            enableRangeProcessing: true
        );
    }
}
