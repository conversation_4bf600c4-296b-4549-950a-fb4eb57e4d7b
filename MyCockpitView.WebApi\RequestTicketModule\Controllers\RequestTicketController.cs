﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;

using MyCockpitView.WebApi.RequestTicketModule.Services;
using MyCockpitView.WebApi.RequestTicketModule.Dtos;
using MyCockpitView.WebApi.RequestTicketModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.RequestTicketModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class RequestTicketController : ControllerBase
{
    private readonly IRequestTicketService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IWFTaskService taskService;
    private readonly ICurrentUserService currentUserService;
    private readonly IContactService contactService;

    public RequestTicketController(EntitiesContext db, IRequestTicketService service, IMapper mapper, IActivityService activityService, IWFTaskService taskService, ICurrentUserService currentUserService,IContactService contactService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.taskService = taskService;
        this.currentUserService = currentUserService;
        this.contactService = contactService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<RequestTicketDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Assignees)
            .Include(x => x.AssignerContact)
            .Include(x => x.Attachments);
        var results = mapper.Map<IEnumerable<RequestTicketDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(RequestTicket))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(RequestTicket))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<RequestTicketDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Assignees)
            .Include(x => x.AssignerContact)
            .Include(x => x.Attachments);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<RequestTicketDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(RequestTicket))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(RequestTicket))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<RequestTicketDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<RequestTicketDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<RequestTicketDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(RequestTicket))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(RequestTicket))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [AllowAnonymous]
    [HttpPost]
    public async Task<ActionResult<RequestTicketDto>> Post(RequestTicketDto dto)
    {
        var Entity = mapper.Map<RequestTicket>(dto);
        if (dto.Assignees != null)
        {
            Entity.Assignees = mapper.Map<List<RequestTicketAssignee>>(dto.Assignees);
        }
        if(dto.Attachments != null)
        {
            Entity.Attachments = mapper.Map<List<RequestTicketAttachment>>(dto.Attachments);
        }
        var id = await service.Create(Entity);


        var responseDto = mapper.Map<RequestTicketDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");


        await service.SendRequestTicket(responseDto.ID);//SEND INITIAL EMAIL

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(RequestTicket))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(RequestTicket))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(RequestTicket), responseDto.ID, $"{responseDto.Title} | {responseDto.Subtitle}",
                               "NextReminder: " + ClockTools.GetIST(responseDto.NextReminderDate).ToString("dd MMM yyyy")
                   + "| Interval: " + responseDto.ReminderInterval.ToString(), "Created");
        }

        if (responseDto.StatusFlag != 1)
            await taskService.StartFlow(nameof(RequestTicket), responseDto.TypeFlag, responseDto.ID);

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<RequestTicketDto>> Put(int id, RequestTicketDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<RequestTicket>(dto));
        var responseDto = mapper.Map<RequestTicketDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(RequestTicket))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(RequestTicket))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(RequestTicket), responseDto.ID, $"{responseDto.Title} | {responseDto.Subtitle}",
                               "NextReminder: " + ClockTools.GetIST(responseDto.NextReminderDate).ToString("dd MMM yyyy")
                   + "| Interval: " + responseDto.ReminderInterval.ToString(), "Updated");
        }

        if (responseDto.StatusFlag == 0)
            await taskService.UpdateTaskDue(nameof(RequestTicket), responseDto.ID);

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<RequestTicketDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(RequestTicket)} not found!");

        await service.Delete(id);

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(RequestTicket), responseDto.ID, $"{responseDto.Title} | {responseDto.Subtitle}",
                               "NextReminder: " + ClockTools.GetIST(responseDto.NextReminderDate).ToString("dd MMM yyyy")
                   + "| Interval: " + responseDto.ReminderInterval.ToString(), "Deleted");
        }


        await taskService.PurgePendingTasks(nameof(RequestTicket), id);

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<RequestTicketDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<RequestTicketDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(RequestTicket))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(RequestTicket))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }

    [HttpGet("SubjectOptions")]
    public async Task<IActionResult> GetSubjectOptions()
    {

        var query = service.Get().Select(x => x.Title).Distinct();


        return Ok(await query.ToListAsync());

    }

    [AllowAnonymous]
    [HttpGet("RequestTicketFollowUp")]
    public async Task<IActionResult> RequestTicketFollowUp()
    {

        var _today = DateTime.UtcNow.Date;
        var _EOD = DateTime.UtcNow.AddDays(1).Date;
        var _openRequests = await service.Get()
                .Where(x => !x.IsVersion)
            .Where(x => x.StatusFlag == 0)
            .Where(x => x.Created < _today)
            .Where(x => x.NextReminderDate < _EOD)
            .OrderBy(x => x.NextReminderDate)
            .ToListAsync();

        var failed = 0;
        foreach (var obj in _openRequests)
        {
            try
            {

                await service.RecordReadonly(obj.ID);

                var _currentReminderDate = obj.NextReminderDate;

                do
                {
                    obj.NextReminderDate = obj.NextReminderDate.AddDays(Decimal.ToDouble(obj.ReminderInterval));

                    if ((obj.NextReminderDate < DateTime.UtcNow))
                    {
                        _currentReminderDate = obj.NextReminderDate;
                    }

                } while (obj.NextReminderDate < DateTime.UtcNow);


                obj.RepeatCount++;
                db.Entry(obj).State = EntityState.Modified;
                await db.SaveChangesAsync();


                if (ClockTools.GetIST(_currentReminderDate).Date == ClockTools.GetISTNow().Date)
                {
                    await service.SendRequestTicket(obj.ID);
                }


                await taskService.UpdateTaskDue(nameof(RequestTicket), obj.ID);
            }
            catch (Exception e)
            {
                failed++;

            }

        }
        return Ok($"{_openRequests.Count - failed} sent. {failed} failed.");

    }
}