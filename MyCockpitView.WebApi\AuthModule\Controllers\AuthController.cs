using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using MyCockpitView.WebApi.AuthModule.Dtos;
using MyCockpitView.WebApi.AuthModule.Entities;
using MyCockpitView.WebApi.AuthModule.Services;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.AuthModule.Controllers;

[Route("[controller]")]
[ApiController]
public class AuthController : ControllerBase
{
    private readonly UserManager<User> _userManager;
    private readonly IMapper _mapper;
    private readonly RoleManager<Role> _roleManager;
    private readonly SignInManager<User> _signInManager;
    private readonly EntitiesContext _context;
    private readonly ILogger<AuthController> _logger;
    private readonly ISharedService sharedService;
    private readonly ILoginSessionService loginSessionService;
    private readonly IContactService contactService;
    private readonly IRefreshTokenService refreshTokenService;
    private readonly IAccessTokenService accessTokenService;
    private readonly ICurrentUserService currentUserService;

    public AuthController(
        IMapper mapper,
        UserManager<User> userManager,
        RoleManager<Role> roleManager,
        SignInManager<User> signInManager,
        EntitiesContext context,
        ILogger<AuthController> logger,
        ISharedService sharedService,
        ILoginSessionService loginSessionService,
        IContactService contactService,
        IRefreshTokenService refreshTokenService,
        IAccessTokenService accessTokenService,
        ICurrentUserService currentUserService
        )
    {
        _mapper = mapper;
        _userManager = userManager;
        _roleManager = roleManager;
        _signInManager = signInManager;
        _context = context;
        this._logger = logger;
        this.sharedService = sharedService;
        this.loginSessionService = loginSessionService;
        this.contactService = contactService;
        this.refreshTokenService = refreshTokenService;
        this.accessTokenService = accessTokenService;
        this.currentUserService = currentUserService;
    }

    [Authorize]
    [HttpPost("Register")]
    public async Task<IActionResult> SignUp(UserSignUpDto userSignUpResource)
    {
        var user = _mapper.Map<UserSignUpDto, User>(userSignUpResource);

        var userCreateResult = await _userManager.CreateAsync(user, userSignUpResource.Password);

        if (!userCreateResult.Succeeded) throw new BadRequestException(userCreateResult.Errors.First().Description);

        _logger.LogInformation($"New User signed up: {user.UserName}");
        return Created(string.Empty, string.Empty);
    }

    [HttpPost("Login")]
    public async Task<IActionResult> Login([FromBody] UserLoginDto request, CancellationToken cancellationToken)
    {

        var user = await _userManager.FindByNameAsync(request.Username);

        if (user is null) throw new BadRequestException($"We were not able to find any user with that username. Please enter a valid username.");

        var signInResult = await _signInManager.PasswordSignInAsync(user, request.Password, false, false);

        if (!signInResult.Succeeded) throw new BadRequestException($"User login failed. Please check the username or password and try again.");


        var _contact = await contactService.Get().Include(x=>x.Appointments)
            .SingleOrDefaultAsync(x => x.Username == request.Username);

        if (_contact == null) throw new BadRequestException("Contact details not found!");

        var refreshToken = refreshTokenService.Generate(user);
        await _context.RefreshTokens.AddAsync(new RefreshToken() { UserID = user.Id, Token = refreshToken }, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        var result = new AuthenticateResponse
        {
            AccessToken = accessTokenService.Generate(user),
            RefreshToken = refreshToken,
            IsChangePassword = user.IsChangePassword,
        };


            //var roles = await _userManager.GetRolesAsync(user);
            //if (          !roles.Any(x => x.Equals(McvConstant.ROLE_MASTER, StringComparison.OrdinalIgnoreCase))                )
            //{

            //    var _sessionCount = await loginSessionService.Get().Where(x=>x.Username==request.Username).CountAsync();

            //    var _allowedLoginCount = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.LOGIN_ALLOWED_COUNT));
            //    if (_sessionCount >= _allowedLoginCount)
            //    {
            //        _logger.LogInformation("Login limit reached! You have already logged in from " + _allowedLoginCount + " devices. Please log out from other devices or contact Admin.");

            //        throw new BadRequestException("Login limit reached! You have already logged in from " + _allowedLoginCount + " devices. Please log out from other devices or contact Admin.");
            //    }
            //}
            var ipAddress = currentUserService.GetRemoteIpAddress();
            var userAgent = currentUserService.GetUserAgent();

            // Device Info
            var os = currentUserService.GetHeaderValue("os");
            var browserName = currentUserService.GetHeaderValue("browser");
            var device = currentUserService.GetHeaderValue("device");
            var deviceType = currentUserService.GetHeaderValue("devicetype");


            var activeAppointment = _contact.Appointments.FirstOrDefault(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED);
            var _devmode = Convert.ToBoolean(await sharedService.GetPresetValue(McvConstant.DEVMODE));
            var _otpEnabled = Convert.ToBoolean(await sharedService.GetPresetValue(McvConstant.LOGIN_OTP_ENABLED));

            var session = new LoginSession
            {
                ContactID = _contact.ID,
                Username = _contact.Username,
                IpAddress = ipAddress,
                UserAgent = !request.UserAgent.IsNullOrEmpty() ? request.UserAgent : userAgent,
                OS = !request.OS.IsNullOrEmpty() ? request.OS : os,
                BrowserName = !request.Browser.IsNullOrEmpty() ? request.Browser : browserName,
                Device = !request.Device.IsNullOrEmpty() ? request.Device : device,
                DeviceType = !request.DeviceType.IsNullOrEmpty() ? request.DeviceType : deviceType,
                Token = refreshToken,
                IsActive = true,
                IsOTPRequired = activeAppointment == null && _otpEnabled && !_devmode && _contact.Username != "<EMAIL>"
            };
                await loginSessionService.Create(session);

            result.SessionID = session.UID;
            result.IsOTPRequired = session.IsOTPRequired;
            _logger.LogInformation($"User session started: {user.UserName}");


        _logger.LogInformation($"User logged in: {user.UserName}");
        return Ok(result);

    }

    [Authorize]
    [HttpPost]
    [Route("Logout")]
    public async Task<IActionResult> Logout([FromBody] UserLogOutDto dto)
    {

            await loginSessionService.LogoutUser(dto.Username, dto.Token);

            return Ok();

    }

    [HttpPost("Refresh")]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenDto request, CancellationToken cancellationToken)
    {

        var isValidRefreshToken = refreshTokenService.Validate(request.Token);
        if (!isValidRefreshToken)
            throw new BadRequestException("Refresh token is not valid.");
        var previousToken =
            await _context.RefreshTokens.FirstOrDefaultAsync(x => x.Token == request.Token,
                cancellationToken);
        if (previousToken is null)
            throw new BadRequestException("Refresh token failed to generate.");

        _context.RefreshTokens.Remove(previousToken);
        await _context.SaveChangesAsync(cancellationToken);

        var user = await _userManager.FindByIdAsync(previousToken.UserID.ToString());
        if (user is null) throw new NotFoundException($"User not found!");

        var refreshToken = refreshTokenService.Generate(user);
        await _context.RefreshTokens.AddAsync(new RefreshToken() { UserID = user.Id, Token = refreshToken }, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        await loginSessionService.UpdateRefreshToken(refreshToken);

        var result = new AuthenticateResponse
        {
            AccessToken = accessTokenService.Generate(user),
            RefreshToken = refreshToken,
            IsChangePassword = user.IsChangePassword,

        };
        return Ok(result);
    }


    [Authorize]
    [HttpDelete("{username}")]
    public async Task<IActionResult> DeleteUser(string username)
    {
        //Only SuperAdmin or Admin can delete users (Later when implement roles)

        var appUser = await _userManager.FindByNameAsync(username);

        IdentityResult result = await _userManager.DeleteAsync(appUser);

        if (!result.Succeeded) new BadRequestException(result.Errors.First().Description);

        var contacts = await contactService.Get().Where(x => x.Username == username).ToListAsync();
        foreach (var contact in contacts)
        {
            contact.Username = null;
            await contactService.Update(contact);
        }

        return Ok();
    }

    [Authorize]
    [HttpPut("Change")]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto Dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var user = await _userManager.FindByNameAsync(Dto.Username);
        user.IsChangePassword = false;

        var result = await _userManager.UpdateAsync(user);

        if (!result.Succeeded) new BadRequestException(result.Errors.First().Description);

        result = await _userManager.ChangePasswordAsync(user, Dto.OldPassword, Dto.NewPassword);

        if (!result.Succeeded) new BadRequestException(result.Errors.First().Description);

        return Ok();

    }

    [Authorize]
    [HttpPut("Reset")]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDto Dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var user = await _userManager.FindByNameAsync(Dto.Username);
        var result = await _userManager.RemovePasswordAsync(user);

        result = await _userManager.AddPasswordAsync(user, Dto.Password);

        if (!result.Succeeded) new BadRequestException(result.Errors.First().Description);

        user.IsChangePassword = Dto.IsChangePassword;
        await _userManager.UpdateAsync(user);
        return Ok();
    }

    [Authorize]
    [HttpGet("Roles")]
    public async Task<IActionResult> GetRoles()
    {


        var _query = _roleManager.Roles.Where(x => !x.IsHidden);
        var results = await _query.ToListAsync();

        return Ok(results);
    }

    [Authorize]
    [HttpGet("RolesByUsername")]
    public async Task<IActionResult> GetRolesByUser(string username)
    {

        var user = await _userManager.Users.SingleOrDefaultAsync(u => u.UserName == username);
        var results = await _userManager.GetRolesAsync(user);

        return Ok(results);
    }

    [Authorize]
    [HttpGet("RoleOptionsByUsername")]
    public async Task<IActionResult> GetRoleOptionsByUsername(string username)
    {

        var user = await _userManager.Users.SingleOrDefaultAsync(u => u.UserName == username);
        var results = await _userManager.GetRolesAsync(user);
        var _appRoles = await _roleManager.Roles.ToListAsync();

        var _result = new HashSet<RoleDto>();
        foreach (var r in _appRoles.Where(x => !x.IsHidden))
        {
            _result.Add(new RoleDto
            {
                Id = r.Id,
                Name = r.Name,
                Title = r.Title,
                Description = r.Description,
                Module = r.Module,
                IsSpecial = r.IsSpecial,
                IsDefault = r.IsDefault,
                IsHidden = r.IsHidden,
                OrderFlag = r.OrderFlag,
                Group = r.Group,
                IsAssigned = results.Any(x => x == r.Name)
            });
        }

        return Ok(_result.OrderBy(x => x.Module).ThenBy(x => x.Name));
    }

    [Authorize]
    [HttpPut("AddRoles")]
    public async Task<IActionResult> AddRolesToUser(AddRemoveRoleDto dto)
    {

        var user = await _userManager.Users.SingleOrDefaultAsync(u => u.UserName == dto.Username);

        var result = await _userManager.AddToRolesAsync(user, dto.Roles);

        if (!result.Succeeded) new BadRequestException(result.Errors.First().Description);

        foreach (var role in dto.Roles)
            _logger.LogInformation(user.UserName + " | " + role + " role added!");
        return Ok();


    }

    [Authorize]
    [HttpPut("RemoveRoles")]
    public async Task<IActionResult> RemoveRolesFromUser(AddRemoveRoleDto dto)
    {
        var user = await _userManager.Users.SingleOrDefaultAsync(u => u.UserName == dto.Username);

        var result = await _userManager.RemoveFromRolesAsync(user, dto.Roles);

        if (!result.Succeeded) new BadRequestException(result.Errors.First().Description);

        foreach (var role in dto.Roles)
            _logger.LogInformation($"User: {user.UserName} role:{role} removed!");

        return Ok();

    }

    [Authorize]
    [HttpPost("Email/verifyOTP")]
    public async Task<IActionResult> VerifyEmailOTP(EmailOTPVerificationDto dto)
    {
            await loginSessionService.VerifyEmailOTP(dto);
            return Ok(new OTPResult("success"));

    }
}

public class OTPResult
{
    public OTPResult(string status)
    {
        this.Status = status;
    }
    public string? Status { get; set; }
}