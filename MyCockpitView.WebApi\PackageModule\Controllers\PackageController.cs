﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.PackageModule.Dtos;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.MeetingModule.Dtos;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using System.Net.Mime;

namespace MyCockpitView.WebApi.PackageModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class PackageController : ControllerBase
{
    private readonly IPackageService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IContactAppointmentService contactAppointmentService;
    private readonly IMeetingAgendaService meetingAgendaService;
    private readonly ISharedService sharedService;
    private readonly IWFTaskService taskService;

    public PackageController(EntitiesContext db, IPackageService service, IMapper mapper, IActivityService activityService, IContactService contactService,IContactAppointmentService contactAppointmentService,IMeetingAgendaService meetingAgendaService, ISharedService sharedService,IWFTaskService taskService  )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.contactAppointmentService = contactAppointmentService;
        this.meetingAgendaService = meetingAgendaService;
        this.sharedService = sharedService;
        this.taskService = taskService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<PackageDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Associations).ThenInclude(s => s.Contact)
                    .Include(x => x.Deliverables).ThenInclude(s => s.PackageDeliverableTaskMaps);
        var results = mapper.Map<IEnumerable<PackageDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Package))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<PackageListDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {


        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Associations).ThenInclude(s => s.Contact)
            .Include(x => x.Project)
                    .Include(x => x.Deliverables).ThenInclude(s => s.PackageDeliverableTaskMaps);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<PackageListDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Package))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        var appointedContacts = await contactAppointmentService.Get()
               .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
               .Select(x => x.ContactID)
               .ToListAsync();

        var agenda = await meetingAgendaService.Get()
            .Where(x => !x.IsVersion)
            .Where(x => !x.IsForwarded)
            .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE || x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING)
            .Where(x => x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING)
            .Where(x => x.PackageID != null)
            .Select(x => new
            {
                x.ID,
                x.PackageID,
                x.ActionByContactID

            })
            .ToListAsync();

        foreach (var obj in results)
        {

            var packageAgenda = agenda.Where(x => x.PackageID == obj.ID);

            obj.IsExternalDependant = packageAgenda.All(x => !appointedContacts.Any(a => a == x.ActionByContactID));
        }


        return Ok(new PagedResponse<PackageListDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<PackageDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<PackageDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Package))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<PackageDto>> Post(PackageDto dto)
    {
        var id = await service.Create(mapper.Map<Package>(dto));
        var responseDto = mapper.Map<PackageDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Package))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Package).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<PackageDto>> Put(int id, PackageDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Package>(dto));
        var responseDto = mapper.Map<PackageDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Package))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Package).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

  

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<PackageDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Package)} not found!");

        await taskService.PurgePendingTasks(nameof(Package), id);

        var _meetingAgendas = await meetingAgendaService.Get()
            .Where(x => !x.IsVersion && !x.IsForwarded)
             .Where(x => x.PackageID != null && x.PackageID == id)
             .ToListAsync();

        foreach (var agenda in _meetingAgendas)
        {
            agenda.PackageID = null;
            db.Entry(agenda).State = EntityState.Modified;
            await db.SaveChangesAsync();

            await meetingAgendaService.AssignAgendaTasks(agenda.ID);

        }

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Package).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<PackageDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<PackageDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Package))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        responseDto.MeetingAgendas = mapper.Map<List<MeetingAgendaDto>>(await meetingAgendaService.Get()
                  .Where(x => !x.IsVersion)
        .Where(x => x.PackageID != null && x.PackageID == responseDto.ID).ToListAsync());

        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        foreach (var attachment in responseDto.Attachments)
        {
            attachment.Url = attachment.Url.Replace(originalHostName, cdnHostName);
            if (attachment.ThumbUrl != null)
                attachment.ThumbUrl = attachment.ThumbUrl.Replace(originalHostName, cdnHostName);
        }

        if (responseDto.CloudFile != null)
        {
            responseDto.CloudFile = responseDto.CloudFile.Replace(originalHostName, cdnHostName);
        }

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("Count")]
    public async Task<IActionResult> GetCount(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {

        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        return Ok(await query.CountAsync());

    }

    [AllowAnonymous]
    [HttpGet("QRCode/{id:guid}")]
    public async Task<IActionResult> GetQRCode(Guid id)
    {

        var rootApi = await sharedService.GetPresetValue(McvConstant.ROOT_API);
        var qr = await sharedService.GenerateQR(new QRRequest
        {
            Url = $"{rootApi}/package/submission/{id}"
        });
        return Ok(new {Value=qr});

    }


    [HttpPost("NextRevision")]
    public async Task<IActionResult> NextRevision([FromBody] PackageDto Dto)
    {

        var _model = mapper.Map<Package>(Dto);

        if (_model != null)
            return Ok(new { Value= await service.GetNextRevision(_model) });

        return BadRequest("Data not found!");

    }

    [HttpGet("Consumed/{id}")]
    public async Task<IActionResult> GetConsumed(int id)
    {

        var query = await service.GetPackageVHrConsumption(id);

        return Ok(query);

    }

    [AllowAnonymous]
    [HttpGet("Preview/{id:int}")]
    public async Task<IActionResult> Preview(int id)
    {
        var submission = await service.Get()
            .SingleOrDefaultAsync(x => x.ID == id);

        if (submission is null)
            return BadRequest("Package Not Found!");

        if (submission.StatusFlag != McvConstant.PACKAGE_STATUSFLAG_SENT)
        {
            var blobUrl = await service.GetSubmissionSet(submission.ID);
            if (blobUrl is not null)
            {
                submission.CloudFile = blobUrl;
                submission.IsSubmissionSetProcessed = false;

                await service.Update(submission);
            }
        }

        return Ok(new { Value=submission.CloudFile});
    }

    [AllowAnonymous]
    [HttpGet("Send/{id:int}")]
    public async Task<IActionResult> Send(int id)
    {
       await service.SendEmail(id);

        return Ok("Submission Sent");
    }

    [AllowAnonymous]
    [HttpGet("EmailPreview/{id:int}")]
    [Produces(MediaTypeNames.Text.Html)]
    public async Task<ContentResult> GetEmailPreview(int id)
    {
        var mailBody = await service.GetSubmissionEmailPreview(id);
        return Content(mailBody, MediaTypeNames.Text.Html);
    }

    [HttpGet("Analysis/{dataType}")]
    public async Task<IActionResult> GetVHrAnalysis(
        string dataType,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        if (!dataType.Equals("full", StringComparison.OrdinalIgnoreCase))
            return BadRequest("dataType not found!");

        var result = await service.GetAnalysisData(
            filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort);

        return Ok(result);
    }

    [AllowAnonymous]
    [HttpGet("Analysis/{dataType}/excel")]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")]
    public async Task<IActionResult> GetVHrAnalysisExcel(
        string dataType,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        if (!dataType.Equals("full", StringComparison.OrdinalIgnoreCase))
            return BadRequest("dataType not found!");

        var report = await service.GetAnalysisExcel(
           filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort);

        if (report is null)
            return BadRequest("Report cannot be generated");

        var fileName = $"PackageAnalysis-{TimeProvider.System.GetUtcNow():dd-MMM-yyyy}.xlsx";

        return File(
            fileContents: report,
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileDownloadName: fileName);
    }


    [HttpGet("Upcoming/Pages")]
    public async Task<ActionResult<PagedResponse<UpcomingPackageDto>>> Get(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.GetUpcomingPackages(
            filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort);

        if (pageSize == 0)
            return Ok(await query.ToListAsync());

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);

        var results = await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync();

        return Ok(new PagedResponse<UpcomingPackageDto>(results, totalCount, totalPages));
    }

    [AllowAnonymous]
    [HttpGet("Report/{reportName}/{id:int}")]
    public async Task<IActionResult> Report(string reportName, int id, [FromQuery] bool inline = false)
    {
        var reportDef = reportName.ToLowerInvariant() switch
        {
            "submissionhistorybrief" => await service.GetSubmissionHistoryBriefReport(id),
            "submissionhistory" => await service.GetSubmissionHistoryDetailReport(id),
            "submissiondirectory" => await service.GetSubmissionDirectoryReport(id),
            "transmittal" => await service.GetTransmittalReport(id),
            "designintent" => await service.GetDesignIntentReport(id),
            _ => null
        };

        if (reportDef?.FileContent is null)
            return BadRequest("Report not generated!");

        return File(
            fileContents: reportDef.FileContent,
            contentType: reportDef.FileContentType,
            fileDownloadName: reportDef.Filename + reportDef.FileExtension,
            enableRangeProcessing: true);
    }


    [HttpGet]
    [Route("StartFlow/{id}")]
    public async Task<IActionResult> StartFlow(int id)
    {

        await service.StartFlow(id);
        return Ok();

    }

    [AllowAnonymous]
    [HttpGet("Submission/{id:guid}")]
    [ProducesResponseType(StatusCodes.Status302Found)]
    public async Task<IActionResult> GetSubmission(Guid id)
    {
        var baseUrl = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_URL_ROOT);
        return RedirectPermanent($"{baseUrl}{id}");
    }

    [AllowAnonymous]
    [HttpGet("UploadSubmissionsToCloud")]
    public async Task<IActionResult> UploadSubmissionsToCloud()
    {

        await service.UploadSubmissionsToCloud();
        return Ok();

    }

    [AllowAnonymous]
    [HttpGet("UpdatePackageConsumption")]
    public async Task<IActionResult> UpdatePackageConsumption()
    {

        await service.UpdatePackageConsumption();
        return Ok();

    }
}