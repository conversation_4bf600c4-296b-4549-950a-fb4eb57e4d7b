{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "SqlServerDbSettings": {"Database": "mcv.staging", "Server": "mycockpitview.in", "Username": "mcv.write", "Password": "McvWriter@2025$$", "Encrypt": "true", "TrustServerCertificate": "true"}, "JwtSettings": {"AccessTokenSecret": "jRKYXQF51hopMQZptbw3GzhHgsaxgrhmjRKYXQF51hopMQZptbw3GzhHgsaxgrhm", "RefreshTokenSecret": "jRKYXQF51hopMQZptbw3GzhHgsaxgrhmjRKYXQF51hopMQZptbw3GzhHgsaxgrhm", "AccessTokenExpirationMinutes": 1440, "RefreshTokenExpirationMinutes": 43200, "Issuer": "https://localhost:7045", "Audience": "https://localhost:7045"}, "AllowedHosts": "*"}