﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.InspectionModule.Services;
using MyCockpitView.WebApi.InspectionModule.Dtos;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.StatusMasterModule;
using MyCockpitView.WebApi.TypeMasterModule;
using DocumentFormat.OpenXml.Office2010.Excel;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.InspectionModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class InspectionController : ControllerBase
{
    private readonly IInspectionService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IWFTaskService taskService;
    private readonly ICurrentUserService currentUserService;

    public InspectionController(EntitiesContext db, IInspectionService service, IMapper mapper, IActivityService activityService, IContactService contactService, IWFTaskService taskService, ICurrentUserService currentUserService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.taskService = taskService;
        this.currentUserService = currentUserService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<InspectionDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);
        var results = mapper.Map<IEnumerable<InspectionDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Inspection))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Inspection))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

          var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        foreach (var obj in results)
        {
            if (currentContact != null)
                obj.IsEditable = await service.IsInspectionEditable(obj.ID, currentContact.ID);

            var _taskDueDate = await taskService.Get()
           .Where(x => x.Entity != null && x.Entity==nameof(Inspection)
           && x.EntityID == obj.ID && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
           .OrderBy(x => x.DueDate)
           .Select(x => x.DueDate)
           .FirstOrDefaultAsync();

            if (_taskDueDate != null)
            {
                obj.IsDelayed = (_taskDueDate < DateTime.UtcNow);
            }
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<InspectionDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<InspectionDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Inspection))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Inspection))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }
        var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        foreach (var obj in results)
        {
            if (currentContact != null)
                obj.IsEditable = await service.IsInspectionEditable(obj.ID, currentContact.ID);

            var _taskDueDate = await taskService.Get()
          .Where(x => x.Entity != null && x.Entity==nameof(Inspection)
          && x.EntityID == obj.ID && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
          .OrderBy(x => x.DueDate)
          .Select(x => x.DueDate)
          .FirstOrDefaultAsync();

            if (_taskDueDate != null)
            {
                obj.IsDelayed = (_taskDueDate < DateTime.UtcNow);
            }
        }
        return Ok(new PagedResponse<InspectionDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<InspectionDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<InspectionDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Inspection))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Inspection))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

         var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
            responseDto.IsEditable = await service.IsInspectionEditable(responseDto.ID, currentContact.ID);

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<InspectionDto>> Post(InspectionDto dto)
    {
        var id = await service.Create(mapper.Map<Inspection>(dto));
        var responseDto = mapper.Map<InspectionDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Inspection))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Inspection))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";
        var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);

        if (currentContact != null)
            responseDto.IsEditable = await service.IsInspectionEditable(responseDto.ID, currentContact.ID);
        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Inspection).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<InspectionDto>> Put(int id, InspectionDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Inspection>(dto));
        var responseDto = mapper.Map<InspectionDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Inspection))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Inspection))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";
        var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);

        if (currentContact != null)
            responseDto.IsEditable = await service.IsInspectionEditable(responseDto.ID, currentContact.ID);
        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Inspection).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<InspectionDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Inspection)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Inspection).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<InspectionDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<InspectionDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Inspection))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Inspection))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("IsEditable/{id}")]
    public async Task<IActionResult> CheckIfEditable(int id)
    {

          var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact == null)
            return Ok(false);

        return Ok(await service.IsInspectionEditable(id, currentContact.ID));

    }

    [HttpGet("IsDelayed/{id}")]
    public async Task<IActionResult> CheckIfDelayed(int id)
    {

        var _taskDueDate = await taskService.Get()
         .Where(x => x.Entity != null && x.Entity==nameof(Inspection)
         && x.EntityID == id && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
         .OrderBy(x => x.DueDate)
         .Select(x => x.DueDate)
         .FirstOrDefaultAsync();

        if (_taskDueDate != null)
        {
            return Ok((_taskDueDate < DateTime.UtcNow));
        }

        return Ok(false);

    }


    [HttpPut("Send/{id}")]
    public async Task<IActionResult> Send(int id)
    {

        await service.SendMinutes(id);
        return Ok();

    }


    [AllowAnonymous]
    [HttpGet("Report/{reportName}/{size}/{id:guid}")]
    public async Task<IActionResult> Report(
        string reportName,
        string size,
        Guid id,
        [FromQuery] string? filters = null,
        [FromQuery] string? sort = null,
        [FromQuery] string output = "PDF",
        [FromQuery] bool inline = false)
    {
        var deserializedFilters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;

        ReportDefinition? reportDef = reportName.ToLower() switch
        {
            "minutes" => await service.GetMinutesReport(size, id, sort),
            "item" => await service.GetItemReport(size, id, sort),
            _ => null
        };

        if (reportDef?.FileContent == null)
        {
            return BadRequest("Report not generated!");
        }

        var contentDisposition = inline ? "inline" : "attachment";

        return File(
            fileContents: reportDef.FileContent,
            contentType: reportDef.FileContentType,
            fileDownloadName: $"{reportDef.Filename}{reportDef.FileExtension}",
            enableRangeProcessing: true
        );
    }
}