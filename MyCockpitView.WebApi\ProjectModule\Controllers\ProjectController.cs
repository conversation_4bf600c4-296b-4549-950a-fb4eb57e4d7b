﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.ProjectModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ProjectModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ProjectController : ControllerBase
{
    private readonly IProjectService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IDesignScriptEntityService designScriptEntityService;
    private readonly IWFTaskService taskService;
    private readonly ICurrentUserService currentUserService;

    public ProjectController(EntitiesContext db, IProjectService service, IMapper mapper, IActivityService activityService, IContactService contactService, IDesignScriptEntityService designScriptEntityService, IWFTaskService taskService, ICurrentUserService currentUserService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.designScriptEntityService = designScriptEntityService;
        this.taskService = taskService;
        this.currentUserService = currentUserService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProjectDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.CompanyAccount);
        var results = mapper.Map<IEnumerable<ProjectDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Project))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ProjectDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.CompanyAccount);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ProjectDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Project))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<ProjectDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ProjectDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ProjectDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Project))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ProjectDto>> Post(ProjectDto dto)
    {
        var id = await service.Create(mapper.Map<Project>(dto));

        await service.RecordVersion(id);

        var responseDto = mapper.Map<ProjectDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Project))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Project), responseDto.ID, $"{responseDto.Code} | {responseDto.Title}",
                               "CompletionDate: " + ClockTools.GetIST(responseDto.ContractCompletionDate).ToString("dd MMM yyyy")
                   + "| Location: " + responseDto.Location, "Created", responseDto.Comment ?? "");
        }


        await designScriptEntityService.SetMasterPhase(responseDto.ID);


        await taskService.StartFlow(nameof(Project), 0, responseDto.ID);

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ProjectDto>> Put(int id, ProjectDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Project>(dto));
        await service.RecordVersion(id);

        var entity= await service.GetById(id);

        await designScriptEntityService.SetMasterPhase(entity.ID);
        await service.ScaffoldAgendaTasks(entity.ID, entity.StatusFlag);

        var responseDto = mapper.Map<ProjectDto>(entity);

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Project))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Project), responseDto.ID, $"{responseDto.Code} | {responseDto.Title}",
                               "CompletionDate: " + ClockTools.GetIST(responseDto.ContractCompletionDate).ToString("dd MMM yyyy")
                   + "| Location: " + responseDto.Location, "Updated", responseDto.Comment ?? "");
        }


        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ProjectDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Project)} not found!");

        await service.Delete(id);

                var username = currentUserService.GetCurrentUsername();       
        var currentContact = await contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
        if (currentContact != null)
        {
            await activityService.LogUserActivity(currentContact, nameof(Project), responseDto.ID, $"{responseDto.Code} | {responseDto.Title}",
                               "CompletionDate: " + ClockTools.GetIST(responseDto.ContractCompletionDate).ToString("dd MMM yyyy")
                   + "| Location: " + responseDto.Location, "Deleted", responseDto.Comment ?? "");
        }


        await taskService.PurgePendingTasks(nameof(Project), id);

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ProjectDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ProjectDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Project))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("Count")]
    public async Task<IActionResult> GetCount(string? filters = null, string? search = null, string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        return Ok(await query.CountAsync());

    }


    [HttpGet("activity/{id}")]
    public async Task<IActionResult> GetActivity(int id, string? filters = null)
    {

        var query = await service.GetProjectActivityData(id, filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null);
        if (query == null)
        {
            return NotFound();
        }

        return Ok(query);
    }

    [HttpGet("Analysis/{dataType}")]
    public async Task<IActionResult> GetAnalysis(string dataType, string? filters = null, string? search = null, string? sort = null)
    {
        return dataType.ToLowerInvariant() switch
        {
            "lastbite" => Ok(await service.GetLastBiteData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            "cashflow" => Ok(await service.GetCashflowData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            "crm" => Ok(await service.GetCRMData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            "full" => Ok(await service.GetProjectAnalysisData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            "estimation" => Ok(await service.GetProjectEstimationData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            _ => BadRequest("dataType not found!")
        };
    }

    [AllowAnonymous]
    [HttpGet("Analysis/{dataType}/excel")]
    public async Task<IActionResult> GetAnalysisExcel(string dataType, string? filters = null, string? search = null, string? sort = null)
    {
        byte[]? report = null;

        report = dataType.ToLowerInvariant() switch
        {
            "lastbite" => await service.GetLastBiteExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort),
            "cashflow" => await service.GetCashflowExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort),
            "crm" => await service.GetCRMExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort),
            "full" => await service.GetProjectAnalysisExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort),
            "estimation" => await service.GetProjectEstimationExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort),
            _ => null
        };

        if (report == null)
        {
            return BadRequest(dataType == null ? "Report cannot be generated" : "dataType not found!");
        }

        var filename = $"{dataType.ToUpper()}-{DateTimeOffset.Now:dd-MMM-yyyy}.xlsx";
        return File(
            report,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename);
    }


    [HttpGet("VHr/{dataType}")]
    public async Task<IActionResult> GetVHrData(string dataType, string? filters = null, string? search = null, string? sort = null)
    {
        return dataType.ToLowerInvariant() switch
        {
            "package" => Ok(await service.GetPackageVHrData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            "meeting" => Ok(await service.GetMeetingVHrData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            "todo" => Ok(await service.GetCRMData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)),
            _ => BadRequest("Invalid dataType")
        };
    }


    [HttpGet("Pages/Script")]
    public async Task<ActionResult<PagedResponse<ScriptProjectDto>>> GetPagesForDS(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);

        var results = await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .Select(x => new ScriptProjectDto
            {
                ID = x.ID,
                UID = x.UID,
                Title = x.Title,
                Code = x.Code,
                StatusFlag = x.StatusFlag
            })
            .ToListAsync();

        var statusMasters = await db.StatusMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .ToListAsync();

        var projectIDs = results.Select(x => x.ID).ToList();

        var dsEntities = await designScriptEntityService.Get()
            .Include(x => x.DataCardMaps)
            .ThenInclude(d => d.DataCard)
            .ThenInclude(a=>a.Attachments)
            .Where(x => projectIDs.Any(p => p == x.ProjectID) &&
                       x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE &&
                       x.Title == "MASTER")
            .SelectMany(x => x.DataCardMaps.Where(c =>
            c.DataCard!=null &&
                c.DataCard.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL &&
                c.DataCard.Category == null),
                (a, b) => new
                {
                    a.ProjectID,
                    ThumbUrl = b.DataCard.Attachments
                        .Where(p => !p.IsDeleted)
                        .Where(p => p.Filename.Contains(".jpg") ||
                                   p.Filename.Contains(".png") ||
                                   p.Filename.Contains(".gif") ||
                                   p.Filename.Contains(".webp") ||
                                   p.Filename.Contains(".jpeg"))
                        .Any()
                        ? b.DataCard.Attachments
                            .Where(p => !p.IsDeleted)
                            .Where(p => p.Filename.Contains(".jpg") ||
                                      p.Filename.Contains(".png") ||
                                      p.Filename.Contains(".gif") ||
                                      p.Filename.Contains(".webp") ||
                                      p.Filename.Contains(".jpeg"))
                            .FirstOrDefault().ThumbUrl
                        : b.DataCard.Attachments
                            .Where(p => !p.IsDeleted)
                            .Where(p => p.Filename.Contains(".jpg") ||
                                      p.Filename.Contains(".png") ||
                                      p.Filename.Contains(".gif") ||
                                      p.Filename.Contains(".webp") ||
                                      p.Filename.Contains(".jpeg"))
                            .FirstOrDefault().Url
                })
            .Where(x => x.ThumbUrl != null)
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.StatusValue = statusMasters.Any(x => x.Value == obj.StatusFlag)
                ? statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? ""
                : "";

            var entity = dsEntities.FirstOrDefault(x => x.ProjectID == obj.ID);
            if (entity != null)
                obj.ImageUrl = entity.ThumbUrl;
        }

        return Ok(new PagedResponse<ScriptProjectDto>(results, totalCount, totalPages));
    }


    [HttpGet("Exists/{title}")]
    public async Task<ActionResult<bool>> Exists(string title)
    {
        var query = await service.Exist(title);
        return Ok(query);
    }


    [HttpGet("NewCode")]
    public async Task<ActionResult<string>> GetNewCode()
    {
        var query = await service.GetNewCodeOrder();
        return Ok(new { Code = query.ToString("0000") });
    }

    [AllowAnonymous]
    [HttpGet("Report/{reportName}/{id}")]
    public async Task<IActionResult> GetReport(
        string reportName,
        int id,
        [FromQuery] string output = "PDF",
        [FromQuery] string? filters = null)
    {
        if (reportName.Equals("ProjectActivityWeekly", StringComparison.OrdinalIgnoreCase) &&
            output.Equals("excel", StringComparison.OrdinalIgnoreCase))
        {
            byte[]? report = await service.GetProjectActivityWeeklyExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null);
            if (report != null)
            {
                return File(
                    report,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "Project Activity.xlsx");
            }
            else
            {
                return BadRequest("Report cannot be generated");
            }
        }

        ReportDefinition? reportDef = reportName.ToLowerInvariant() switch
        {
            "projectactivity" => await service.GetProjectActivityReport(id),
            "projectactivityweekly" => await service.GetProjectActivityWeeklyReport(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null),
            _ => null
        };

        if (reportDef == null || reportDef.FileContent == null)
        {
            return BadRequest(reportName == null ? "Report not generated!" : "No matching Module or Report found!");
        }

        return File(
            reportDef.FileContent,
            reportDef.FileContentType,
            reportDef.Filename + reportDef.FileExtension);
    }

    [AllowAnonymous]
    [HttpGet("LockProjects")]
    public async Task<IActionResult> LockProjects()
    {
        await service.LockProjects();
        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("ResumeTaskFlow")]
    public async Task<IActionResult> ResumeTaskFlow()
    {
        await service.ResumeTaskFlow();
        return Ok();
    }
}

public class ScriptProjectDto
{
    public int ID { get; set; }
    public Guid UID { get; set; }
    public string? Code { get; set; }
    public string? Title { get; set; }
    public string? ImageUrl { get; set; }
    public string? StatusValue { get; set; }
    public int StatusFlag { get; set; }
}