﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.AuthModule.Entities;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.Services;

public interface ICurrentUserService
{
    string? GetCurrentUsername();
    string? GetRemoteIpAddress();
    string GetUserAgent();
    Dictionary<string, string> GetRequestHeaders();
    string GetHeaderValue(string headerName);
}

public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CurrentUserService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string? GetCurrentUsername()
    {
        return _httpContextAccessor.HttpContext?.User?.Identity?.Name;
    }

    public string? GetRemoteIpAddress()
    {
        return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
    }

    public string GetUserAgent()
    {
        return _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].ToString() ?? string.Empty;
    }

    public Dictionary<string, string> GetRequestHeaders()
    {
        var headers = new Dictionary<string, string>();

        if (_httpContextAccessor.HttpContext?.Request.Headers == null)
            return headers;

        foreach (var header in _httpContextAccessor.HttpContext.Request.Headers)
        {
            headers[header.Key] = header.Value.ToString();
        }

        return headers;
    }

    public string GetHeaderValue(string headerName)
    {
        return _httpContextAccessor.HttpContext?.Request.Headers[headerName].ToString() ?? string.Empty;
    }
}
