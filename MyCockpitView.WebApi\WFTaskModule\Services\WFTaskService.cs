using System.Data;
using DocumentFormat.OpenXml.Vml.Office;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.Excel;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using MyCockpitView.WebApi.ExpenseModule.Services;
using MyCockpitView.WebApi.HabitModule.Entities;
using MyCockpitView.WebApi.HabitModule.Services;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.InspectionModule.Services;
using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.LeaveModule.Services;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.RequestTicketModule.Entities;
using MyCockpitView.WebApi.RequestTicketModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.TodoModule.Entities;
using MyCockpitView.WebApi.TodoModule.Services;
using MyCockpitView.WebApi.WFStageModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.WFTaskModule.Services;


public class WFTaskService : BaseEntityService<WFTask>, IWFTaskService
{

    public WFTaskService(EntitiesContext db) : base(db)
    {
    }

    private readonly ICurrentUserService currentUserService;

    public WFTaskService(
        EntitiesContext db,
        ICurrentUserService currentUserService) : base(db)
    {
        this.currentUserService = currentUserService;
    }

    public IQueryable<WFTask> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<WFTask> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Entity != null && x.Entity == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.EntityID != null && x.EntityID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("AssignerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("AssignerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.AssignerContactID == isNumeric);
                }
                _query = _query.Where(x => x.AssignerContactID != null).Where(predicate);
            }


            if (Filters.Where(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("dueDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("dueDate", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.DueDate.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("completedDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("completedDate", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.CompletedDate != null && x.CompletedDate.Value.Date == result.Date);
            }


            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate >= result || x.DueDate >= result || (x.CompletedDate != null && x.CompletedDate.Value >= result));
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.StartDate < end || x.DueDate < end || (x.CompletedDate != null && x.CompletedDate.Value < end));

            }
            if (Filters.Where(x => x.Key.Equals("StartDateRangeStart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("StartDateRangeStart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("StartDateRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("StartDateRangeEnd", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.StartDate < end);

            }
            if (Filters.Where(x => x.Key.Equals("DueDateRangeStart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("DueDateRangeStart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.DueDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("DueDateRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("DueDateRangeEnd", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.DueDate < end);

            }

            if (Filters.Where(x => x.Key.Equals("completedDateRangeStart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("completedDateRangeStart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.CompletedDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("completedDateRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("completedDateRangeEnd", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.CompletedDate < end);

            }

            if (Filters.Where(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Convert.ToBoolean(Filters.First(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Value);

                var predicate = PredicateBuilder.False<WFTask>();

                if (_item)
                {
                    predicate = predicate.Or(x => (x.StatusFlag != 1 && x.DueDate < DateTime.UtcNow)
                        || (x.StatusFlag == 1 && x.DueDate < x.CompletedDate));
                }
                else
                {
                    predicate = predicate.Or(x => (x.StatusFlag != 1 && x.DueDate >= DateTime.UtcNow)
                        || (x.StatusFlag == 1 && x.DueDate >= x.CompletedDate));
                }

                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("IsPreAssignedTimeTask", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Where(x => x.IsPreAssignedTimeTask);
            }
            if (Filters.Where(x => x.Key.Equals("IsStudioWork", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Where(x => x.Entity == nameof(Package) && x.StageIndex == 3);
            }
            if (Filters.Where(x => x.Key.Equals("isStarted", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Where(x => x.StatusFlag == 2);
            }

            if (Filters.Where(x => x.Key.Equals("isPaused", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Where(x => x.StatusFlag == 3);
            }

            if (Filters.Where(x => x.Key.Equals("wfstagecode", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("wfstagecode", StringComparison.OrdinalIgnoreCase));

                _query = _query.Where(x => x.WFStageCode == _item.Value);
            }

            if (Filters.Where(x => x.Key.Equals("IsAssessmentRequired", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Where(x => x.IsAssessmentRequired);
            }

            if (Filters.Where(x => x.Key.Equals("IsAssessmentPending", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Include(x => x.Assessments).Where(x => x.IsAssessmentRequired)
                    .Where(x => !x.Assessments.Any() && x.AssessmentPoints == 0);
            }

            if (Filters.Where(x => x.Key.Equals("timelineRangeStart", StringComparison.OrdinalIgnoreCase)).Any()
                && Filters.Where(x => x.Key.Equals("timelineRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var start = Convert.ToDateTime(Filters.First(x => x.Key.Equals("timelineRangeStart", StringComparison.OrdinalIgnoreCase)).Value);

                var end = Convert.ToDateTime(Filters.First(x => x.Key.Equals("timelineRangeEnd", StringComparison.OrdinalIgnoreCase)).Value);

                _query = _query.Where(x =>
                (x.StartDate >= start && x.StartDate < end) //start is within range
                || (x.DueDate > start && x.DueDate <= end) //end is within range
                || (x.StartDate <= start && x.DueDate >= end) // range contains the entire period
                );
            }

            if (Filters.Where(x => x.Key.Equals("OutcomeFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("OutcomeFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.OutcomeFlag == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != "")
        {

            _query = _query
               .Include(x => x.Contact);

            _query = _query.Where(s => s.Title.ToLower().Contains(Search.ToLower())
                          || s.Subtitle.ToLower().Contains(Search.ToLower())
                          || s.Entity.ToLower().Contains(Search.ToLower())
                          || (s.Contact.FirstName + " " + s.Contact.LastName).ToLower().Contains(Search.ToLower())
                          );

        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("created", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Created);

                else if (key.Trim().Equals("created desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Created);

                else if (key.Trim().Equals("modified", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Modified);

                else if (key.Trim().Equals("modified desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Modified);

                else if (key.Trim().Equals("startdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.StartDate);

                else if (key.Trim().Equals("startdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.StartDate);

                else if (key.Trim().Equals("duedate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.DueDate);

                else if (key.Trim().Equals("duedate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.DueDate);

                else if (key.Trim().Equals("completeddate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.CompletedDate);


                else if (key.Trim().Equals("completeddate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.CompletedDate);
            }

            return _orderedQuery;
        }


        return _query.OrderBy(x => x.DueDate);

    }
    public async Task<WFTask?> GetById(int Id)
    {

        return await db.WFTasks.AsNoTracking()
             .Include(x => x.Contact)
              .Include(x => x.Assigner)
               .Include(x => x.Attachments)
                .Include(x => x.TimeEntries).ThenInclude(t => t.Contact)
                .Include(x => x.Assessments)
                .Include(x => x.Requests)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public string GetActivityStatus(WFTask _wfTask)
    {
        if (_wfTask.StatusFlag == 1)
        {
            if (_wfTask.OutcomeFlag == 1)
                return "Approved";
            else if (_wfTask.OutcomeFlag == -1)
                return "Rejected";
            else
                return "Completed";
        }
        else if (_wfTask.StatusFlag == 0)
        {
            return "Updated";
        }
        else if (_wfTask.StatusFlag == 2)
        {
            return "Started";
        }
        else if (_wfTask.StatusFlag == 3)
        {
            return "Paused";
        }


        return "Updated";
    }

    public int GetActivityStatusFlag(WFTask Task)
    {
        if (Task.StatusFlag == 1)
        {
            if (Task.OutcomeFlag == 1)
                return 1;
            else if (Task.OutcomeFlag == -1)
                return 2;
            else
                return 1;
        }

        return Task.StatusFlag;
    }


    public async Task Update(WFTask UpdatedTask, IEnumerable<Assessment>? Assessments = null)
    {

        using (var transaction = db.Database.BeginTransaction())
        {
            var _wfTask = await Get()
            .Where(x => x.ID == UpdatedTask.ID)
            .SingleOrDefaultAsync();

            if (_wfTask == null) throw new EntityServiceException("Task not found");

            //record OldValues
            var _oldStartDate = _wfTask.StartDate;
            var _oldDueDate = _wfTask.DueDate;
            var _oldStage = _wfTask.WFStageCode;
            var _oldStatusFlag = _wfTask.StatusFlag;

            //apply Update
            _wfTask = UpdatedTask;

            //re-calculate if MHrAssigned changed
            _wfTask.VHrAssigned = _wfTask.MHrAssigned * _wfTask.ManValue;
            _wfTask.VHrAssignedCost = _wfTask.VHrAssigned * _wfTask.VHrRate;

            //if StartDate or DueDate updated
            if (_wfTask.StatusFlag == 0
                && _wfTask.IsPreAssignedTimeTask
                && (_oldDueDate != _wfTask.DueDate || _oldStartDate != _wfTask.StartDate)
                )
                await ValidateTask(_wfTask, true);

            // if Status is Started/Paused/Completed maintain StartDate
            if (_wfTask.StatusFlag != 0)
                //_wfTask.StartDate = _oldStartDate;

                //maintain stageCode
                _wfTask.WFStageCode = _oldStage;

            //if StatuFlag updated
            if (_oldStatusFlag != _wfTask.StatusFlag)
            {
                if (_wfTask.StatusFlag == 1) //completed
                {
                    var _history = "Completed [" + ClockTools.GetISTNow().ToString("dd MMM yyyy HH:mm") + "]";
                    _wfTask.History = _wfTask.History != null ? _wfTask.History + " " + _history : _history;

                    _wfTask.CompletedDate = DateTime.UtcNow;
                }
                else if (_wfTask.StatusFlag == 2) //start
                {
                    var _history = "Started [" + ClockTools.GetISTNow().ToString("dd MMM yyyy HH:mm") + "]";
                    _wfTask.History = _wfTask.History != null ? _wfTask.History + " " + _history : _history;

                    //check for any other tasks inprogress & pause them
                    await PauseOtherActiveTasks(_wfTask.ID, _wfTask.ContactID);
                }
                else if (_wfTask.StatusFlag == 3) //Paused
                {
                    var _history = "Paused [" + ClockTools.GetISTNow().ToString("dd MMM yyyy HH:mm") + "]";
                    _wfTask.History = _wfTask.History != null ? _wfTask.History + " " + _history : _history;
                }
            }

            db.Entry(_wfTask).State = EntityState.Modified;
            await db.SaveChangesAsync();

            if (_oldStatusFlag != _wfTask.StatusFlag)
            {
                if (_wfTask.WFStageCode != null)
                {
                    await CompleteTaskStage(_wfTask.ID, false);

                }
                else if (_wfTask.Entity==nameof(Leave)) //Leave Task
                {

                    //check for ALL-WIN
                    var isAllDone = await IsAllTasksCompleted(_wfTask, 1);
                    //var isAllWin = await IsAllApproved(_wfTask, 1);

                    //next stage
                    if (isAllDone)
                    {
                        await PurgePendingTasks(_wfTask);

                        var leaveService = new LeaveService(db);
                        await leaveService.LeaveStage2(_wfTask);
                    }

                }
                else if (_wfTask.Entity==nameof(Package))
                {

                    await CompletePackageTasks(_wfTask.ID, GetActivityStatus(_wfTask));

                }
            }

            if (Assessments != null && Assessments.Any())
            {

                var _assessments = await db.Assessments.AsNoTracking()
             .Where(x => x.WFTaskID != null && x.WFTaskID == _wfTask.ID)
             .ToListAsync();

                foreach (var _entity in _assessments)
                {
                    db.Entry(_entity).State = EntityState.Deleted;
                }


                db.Assessments.AddRange(Assessments);
                await db.SaveChangesAsync();
            }

            //re-calculate VHR data
            await RecalculateVHrData(_wfTask);

            transaction.Commit();
        }

    }



    public async Task RecalculateVHrData(WFTask wfTask)
    {

        var sharedService = new SharedService(db); ;
        var _appointment = await sharedService.GetLastAppointment(wfTask.ContactID);
        if (_appointment != null)
        {
            wfTask.ManValue = _appointment.ManValue;
            wfTask.CompanyID = _appointment.CompanyID;
        }

        try
        {
            wfTask.VHrRate = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));
        }
        catch
        {
            wfTask.VHrRate = 0;
        }
        wfTask.MHrConsumed = Math.Round(await db.TimeEntries.AsNoTracking()
            .Where(x => x.WFTaskID != null && x.WFTaskID == wfTask.ID).AnyAsync() ?
            await db.TimeEntries.AsNoTracking()
            .Where(x => x.WFTaskID != null && x.WFTaskID == wfTask.ID).Select(x => x.ManHours).SumAsync()
            : 0, 2);

        if (wfTask.MHrAssigned == 0)
        {
            wfTask.MHrAssigned = wfTask.MHrConsumed;
        }

        var _assessments = await db.Assessments.AsNoTracking()
          .Where(x => x.WFTaskID != null && x.WFTaskID == wfTask.ID && x.ScoredPoints > 0)
          .ToListAsync();

        wfTask.AssessmentPoints = _assessments.Any() ?
           _assessments.Select(x => x.ScoredPoints).Sum()
           : 0.0m;

        //wfTask.AssessmentRemark = _assessments.Any() ?
        //  _assessments.Select(x => x.Comment).FirstOrDefault()
        //  : null;

        if (wfTask.IsAssessmentRequired)
            wfTask.MHrAssessed = wfTask.MHrAssigned * wfTask.AssessmentPoints / 10.0m;
        else
            wfTask.MHrAssessed = wfTask.MHrAssigned;

        wfTask.VHrAssigned = wfTask.MHrAssigned * wfTask.ManValue;
        wfTask.VHrAssignedCost = wfTask.VHrAssigned * wfTask.VHrRate;
        wfTask.VHrConsumed = wfTask.MHrConsumed * wfTask.ManValue;
        wfTask.VHrAssessed = wfTask.MHrAssessed * wfTask.ManValue;
        wfTask.VHrConsumedCost = wfTask.VHrConsumed * wfTask.VHrRate;
        wfTask.VHrAssessedCost = wfTask.VHrAssessed * wfTask.VHrRate;

        await base.Update(wfTask);

    }


    public async Task<int> CreateManual(WFTask Entity)
    {


        if (Entity.Entity == null || Entity.EntityID == null) throw new EntityServiceException("Task Entity not found! Please try again.");

        if (Entity.IsPreAssignedTimeTask)
        {
            if (Entity.StartDate < DateTime.UtcNow) throw new EntityServiceException("Task start is before current time!");

            if (Entity.DueDate < DateTime.UtcNow) throw new EntityServiceException("Task end is before current time!");

            if (Entity.StartDate > Entity.DueDate) throw new EntityServiceException("Task start & end is invalid! Please select again!");


            //if ((Entity.DueDate - Entity.StartDate).TotalHours > StudioTaskAllowedDuration) throw new CustomException("Task can be assigned for " + StudioTaskAllowedDuration + " hours only!");

            var _query = Get()
                        .Where(x => x.IsPreAssignedTimeTask)
                        .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PAUSED || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_STARTED)
                        .Where(x => x.ContactID == Entity.ContactID);

            var _startDate = Entity.StartDate.AddMinutes(1);
            var _dueDate = Entity.DueDate.AddMinutes(-1);
            _query = _query
                     .Where(x => (x.StartDate == _startDate) //equal start
                                || (x.DueDate == _dueDate) //equal end
                                || (x.StartDate < _startDate && x.DueDate > _dueDate) //inside
                                || (x.StartDate > _startDate && x.DueDate < _dueDate) //outside
                                || (x.StartDate < _startDate && x.DueDate > _startDate) //start overlapp
                                || (x.StartDate > _startDate && x.StartDate < _dueDate) //end overlapp

                     );


            if (await _query.AnyAsync())
            {
                var _first = await _query.FirstOrDefaultAsync();

                throw new EntityServiceException("Task already exists!"
                    + " \n" + _first.Title + " | " + _first.Subtitle
                    + " \n [" + ClockTools.GetIST(_first.StartDate).ToString("dd MMM yyyy HH:mm") + "-" + ClockTools.GetIST(_first.DueDate).ToString("dd MMM yyyy HH:mm") + "]");
            };
        }
        await ValidateTask(Entity, false, true, false);

        return await CreateTask(Entity, Entity.IsPreAssignedTimeTask, false, true);

    }







    public async Task Delete(int Id)
    {

        var entity = await Get()
            .Include(x => x.TimeEntries)
            .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.ID == Id);

        var timeentryervice = new TimeEntryService(db);
        foreach (var x in entity.TimeEntries)
        {
            await timeentryervice.Delete(x.ID);
        }

        var attachmentService = new BaseAttachmentService<WFTaskAttachment>(db);
        foreach (var x in entity.Attachments)
        {
            await attachmentService.Delete(x.ID);
        }


        await base.Delete(Id);

    }

    public async Task PurgePendingTasks(string Entity, int EntityID)
    {

        if (await Get()
          .Where(x => x.Entity != null && x.EntityID == EntityID && x.Entity == Entity)
         .Where(x => x.StatusFlag == 2).AnyAsync())
            throw new Exception("One of the tasks is InProgress. Please complete the task and try again!");

        var _tasks = await Get()
          .Where(x => x.Entity != null && x.EntityID == EntityID && x.Entity == Entity)
         .Where(x => x.StatusFlag != 1 && x.StatusFlag != -1)
          .ToListAsync();

        foreach (var item in _tasks)
        {
            await Delete(item.ID);
        }

    }




    public async Task AutoPauseTasks()
    {

        var _tasks = await Get()
            .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_STARTED)
            .ToListAsync();

        foreach (var item in _tasks)
        {


            var _history = "Auto Paused [" + ClockTools.GetISTNow().ToString("dd MMM yyyy HH:mm") + "]";
            item.History = item.History != null ? item.History + " " + _history : _history;
            //item.CompletedDate = DateTime.UtcNow;



            if (item.WFStageCode != null)
            {
                item.StatusFlag = McvConstant.WFTASK_STATUSFLAG_PAUSED;
                item.OutcomeFlag = McvConstant.WFTASK_OUTCOME_PAUSE;
                await base.Update(item);
                await CompleteTaskStage(item.ID);
            }
            else
            {
                item.StatusFlag = 1; //complete
                await Update(item);

                //using (var _timeEntryService = new TimeEntryServices())
                //{
                //    await _timeEntryService.EndTimeLog(item.ID);
                //}
            }
            await RecalculateVHrData(item);
        }

    }

    public async Task CheckTaskCompletedDate()
    {

        var _tasks = await Get()
            .Where(x => x.StatusFlag == 1 && x.CompletedDate == null)
            .ToListAsync();

        foreach (var item in _tasks)
        {
            item.CompletedDate = item.Modified; //completed
            await Update(item);
        }

    }

    public double GetDuration(DateTime Created, DateTime DueDate)
    {

        return ClockTools.GetDifference(Created, DueDate).TotalHours;

    }

    public async Task<WFTask> GetActiveStage(string Entity, int EntityID)
    {

        return await Get()
            .AsNoTracking()
            .OrderByDescending(x => x.Created)
            .FirstOrDefaultAsync(x => x.Entity != null && x.Entity==Entity
                                    && x.EntityID == EntityID
                                        && x.StatusFlag == 0);

    }

    public async Task<IEnumerable<String>> GetEntityOptions()
    {

        return await Get()
            .Where(x => x.Entity != null)
            .Select(x => x.Entity)
            .Distinct()
            .ToListAsync();

    }

    public async Task<IEnumerable<WFTaskAnalysisDto>> GetAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _tasks = Get()
          .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
                .Where(x => x.Entity != null && x.EntityID != null);

        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _tasks = _tasks.Where(x => x.CompletedDate != null && x.CompletedDate.Value >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value).AddDays(1);

                _tasks = _tasks.Where(x => x.CompletedDate != null && x.CompletedDate.Value < result);

            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _tasks = _tasks.Where(predicate);
            }
        }

        var _query = _tasks
            .Include(x => x.Contact)
            .Include(x => x.Assigner)
            .Include(x => x.Assessments)
            .Include(x => x.Requests)
              .Select(x => new
              {
                  WFTaskID = x.ID,
                  x.ContactID,
                  Person = x.Contact.FullName,
                  AssignedBy = x.Assigner.FullName,
                  TaskTitle = x.Title,
                  x.Entity,
                  x.EntityID,
                  EntityTitle = x.Subtitle,
                  Revision = x.StageRevision,
                  WorkDescription = x.Description,

                  x.StartDate,
                  x.DueDate,
                  CompletedDate = x.CompletedDate != null ?
                  x.CompletedDate.Value :
                  (DateTime?)null,


                  CommentOnCompletion = x.Comment,

                  IsTimeBoundTask = x.IsPreAssignedTimeTask,

                  MHrAssigned = x.IsPreAssignedTimeTask ? (x.MHrAssigned) : 0,
                  x.MHrConsumed,

                  x.MHrAssessed,
                  x.VHrRate,
                  x.ManValue,
                  VHrAssigned = x.IsPreAssignedTimeTask ? x.VHrAssigned : 0,
                  x.VHrConsumed,
                  x.VHrAssessed,

                  x.VHrAssignedCost,
                  x.VHrConsumedCost,
                  x.VHrAssessedCost,

                  Status = db.StatusMasters.Where(s => s.Entity != null && s.Entity==nameof(WFTask) && s.Value == x.StatusFlag).Any() ?
                  db.StatusMasters.Where(s => s.Entity != null && s.Entity==nameof(WFTask) && s.Value == x.StatusFlag).FirstOrDefault().Title : "UNDEFINED",
                  x.AssessmentRemark,
                  IsAssessmentApplicable = x.IsAssessmentRequired,
                  x.AssessmentPoints,
                  Assessments = x.Assessments.Select(a => new
                  {
                      Category = a.Category,
                      Comment = a.Comment,
                      Points = a.Points,
                      ScoredPoints = a.ScoredPoints,
                  }),
                  x.Requests
              });

        if (Search != null && Search != "")
        {
            var keywords = Search.Trim().ToLower().Split(' ');
            foreach (var key in keywords)
            {
                _query = _query.Where(s => s.Person.ToLower().Contains(key)
                                  || s.TaskTitle.ToLower().Contains(key)
                                  || s.Entity.ToLower().Contains(key)
                                  || s.EntityTitle.ToLower().Contains(key)
                                  );
            }
        }

        _query = _query
            .OrderByDescending(x => x.CompletedDate);

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {

                if (key.Trim().Equals("startdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.StartDate);

                else if (key.Trim().Equals("startdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.StartDate);

                else if (key.Trim().Equals("duedate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.DueDate);

                else if (key.Trim().Equals("duedate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.DueDate);

                else if (key.Trim().Equals("completeddate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.CompletedDate);

                else if (key.Trim().Equals("completeddate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.CompletedDate);
            }

        }

        var _results = await _query
           .ToListAsync();

        return _results.Select(x => new WFTaskAnalysisDto
        {
            WFTaskID = x.WFTaskID,
            ContactID = x.ContactID,
            Person = x.Person,
            AssignedBy = x.AssignedBy,
            TaskTitle = x.TaskTitle,
            Entity = x.Entity,
            EntityID = x.EntityID,
            EntityTitle = x.EntityTitle,
            Revision = x.Revision,
            WorkDescription = x.WorkDescription,

            StartDate = x.StartDate,
            DueDate = x.DueDate,
            CompletedDate = x.CompletedDate != null ?
                 x.CompletedDate.Value :
                 (DateTime?)null,
            Delay = x.CompletedDate != null && x.CompletedDate > x.DueDate ? Convert.ToDecimal((x.CompletedDate.Value - x.DueDate).TotalHours) : 0,

            CommentOnCompletion = x.CommentOnCompletion,

            IsTimeBoundTask = x.IsTimeBoundTask,

            MHrAssigned = x.MHrAssigned,
            MHrConsumed = x.MHrConsumed,

            MHrAssessed = x.MHrAssessed,
            VHrRate = x.VHrRate,
            ManValue = x.ManValue,
            VHrAssigned = x.VHrAssigned,
            VHrConsumed = x.VHrConsumed,
            VHrAssessed = x.VHrAssessed,

            VHrAssignedCost = x.VHrAssignedCost,
            VHrConsumedCost = x.VHrConsumedCost,
            VHrAssessedCost = x.VHrAssessedCost,

            Status = x.Status,
            AssessmentRemark = x.AssessmentRemark,
            IsAssessmentApplicable = x.IsAssessmentApplicable,
            AssessmentPoints = x.AssessmentPoints,
            AssessmentSummary = string.Join(",", x.Assessments.Select(a => a.Category + ":" + a.ScoredPoints)),
            Assessments = x.Assessments.Select(a => new WFTaskAnalysisAssessment
            {
                Category = a.Category,
                Comment = a.Comment,
                Points = a.Points,
                ScoredPoints = a.ScoredPoints,
            }).ToList(),
            Requests = x.Requests.Select(a => new WFTaskRequest
            {
                OriginalMHr = a.OriginalMHr,
                RequestedMHr = a.RequestedMHr,
                OriginalDueDate = a.OriginalDueDate,
                RequestedDueDate = a.RequestedDueDate,
                Comment = a.Comment,
                RequestMessage = a.RequestMessage,
                Status = a.StatusFlag == 2 ? "Approved" : (a.StatusFlag == -1 ? "Rejected" : "Pending"),
                Created = a.Created
            }).ToList()
        });

    }

    public async Task<WFTaskAnalysisDto> GetAnalysisDataTotal(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {


        var _query = await GetAnalysisData(Filters, Search, Sort);

        return new WFTaskAnalysisDto
        {
            Delay = _query.Select(x => x.Delay).Sum(),
            MHrAssigned = _query.Select(x => x.MHrAssigned).Sum(),
            MHrConsumed = _query.Select(x => x.MHrConsumed).Sum(),
            MHrAssessed = _query.Select(x => x.MHrAssessed).Sum(),
            VHrAssigned = _query.Select(x => x.VHrAssigned).Sum(),
            VHrConsumed = _query.Select(x => x.VHrConsumed).Sum(),
            VHrAssessed = _query.Select(x => x.VHrAssessed).Sum(),
            VHrAssignedCost = _query.Select(x => x.VHrAssignedCost).Sum(),
            VHrConsumedCost = _query.Select(x => x.VHrConsumedCost).Sum(),
            VHrAssessedCost = _query.Select(x => x.VHrAssessedCost).Sum(),
        };

    }

    public async Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();
        var _data = await GetAnalysisData(Filters, Search, Sort);

        _dataSet.Tables.Add(DataTools.ToDataTable(_data.Select(x => new
        {
            StartDate = ClockTools.GetIST(x.StartDate),
            CompletedDate = x.CompletedDate != null ? ClockTools.GetIST(x.CompletedDate.Value) : x.CompletedDate,
            DueDate = ClockTools.GetIST(x.DueDate),
            x.WFTaskID,
            x.ContactID,
            x.Person,
            x.AssignedBy,
            x.TaskTitle,
            x.Entity,
            x.EntityID,
            x.EntityTitle,
            x.Revision,
            x.WorkDescription,
            x.CommentOnCompletion,
            x.IsTimeBoundTask,
            x.MHrAssigned,
            x.MHrConsumed,
            x.MHrAssessed,
            x.VHrRate,
            x.ManValue,
            x.VHrAssigned,
            x.VHrConsumed,
            x.VHrAssessed,
            x.VHrAssignedCost,
            x.VHrConsumedCost,
            x.VHrAssessedCost,
            x.Status,
            x.AssessmentRemark,
            x.IsAssessmentApplicable,
            x.AssessmentPoints,
            x.AssessmentSummary,


        })));

        return ExcelUtility.ExportExcel(_dataSet);

    }

    public async Task<byte[]> GetVHrAnalysisExcel(IEnumerable<WFTaskVHrAnalysisDto> Data)
    {
        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable(Data.Select(x => new
        {
            //FromDate=x.FromDate.Kind==DateTimeKind.Utc? ClockTools.GetIST(x.FromDate):x.FromDate,
            //ToDate= x.ToDate.Kind == DateTimeKind.Utc ? ClockTools.GetIST(x.ToDate) : x.ToDate,
            x.ContactID,
            x.Person,
            x.CurrentManValue,
            x.CurrentVHrRate,
            x.ExpectedMHr,
            x.ExpectedVHr,
            x.MHr,
            x.VHr,
            x.Remuneration,

            EarnedFromPackages_Mhr = x.EarnedInSelfPackages != null ? x.EarnedInSelfPackages.MHrAssessed : 0,
            EarnedFromPackages_Vhr = x.EarnedInSelfPackages != null ? x.EarnedInSelfPackages.VHrAssessed : 0,
            EarnedFromPackages_Remuneration = x.EarnedInSelfPackages != null ? x.EarnedInSelfPackages.VHrAssessedCost : 0,

            EarnedFromOthersPackages_Mhr = x.RecordedInOthersPackages != null ? x.RecordedInOthersPackages.MHrAssessed : 0,
            EarnedFromOthersPackages_Vhr = x.RecordedInOthersPackages != null ? x.RecordedInOthersPackages.VHrAssessed : 0,
            EarnedFromOthersPackages_Remuneration = x.RecordedInOthersPackages != null ? x.RecordedInOthersPackages.VHrAssessedCost : 0,

            RecordedForMeetings_Mhr = x.RecordedForMeetings != null ? x.RecordedForMeetings.MHrAssessed : 0,
            RecordedForMeetings_Vhr = x.RecordedForMeetings != null ? x.RecordedForMeetings.VHrAssessed : 0,
            RecordedForMeetings_Remuneration = x.RecordedForMeetings != null ? x.RecordedForMeetings.VHrAssessedCost : 0,

            RecordedForInspections_Mhr = x.RecordedForInspections != null ? x.RecordedForInspections.MHrAssessed : 0,
            RecordedForInspections_Vhr = x.RecordedForInspections != null ? x.RecordedForInspections.VHrAssessed : 0,
            RecordedForInspections_Remuneration = x.RecordedForInspections != null ? x.RecordedForInspections.VHrAssessedCost : 0,

            RecordedInProjectTodo_Mhr = x.RecordedInProjectTodo != null ? x.RecordedInProjectTodo.MHrAssessed : 0,
            RecordedInProjectTodo_Vhr = x.RecordedInProjectTodo != null ? x.RecordedInProjectTodo.VHrAssessed : 0,
            RecordedInProjectTodo_Remuneration = x.RecordedInProjectTodo != null ? x.RecordedInProjectTodo.VHrAssessedCost : 0,
            RecordedForInquiries_Mhr = x.RecordedForInquiries != null ? x.RecordedForInquiries.MHrAssessed : 0,
            RecordedForInquiries_Vhr = x.RecordedForInquiries != null ? x.RecordedForInquiries.VHrAssessed : 0,
            RecordedForInquiries_Remuneration = x.RecordedForInquiries != null ? x.RecordedForInquiries.VHrAssessedCost : 0,
            RecordedInTodo_Mhr = x.RecordedInTodo != null ? x.RecordedInTodo.MHrAssessed : 0,
            RecordedInTodo_Vhr = x.RecordedInTodo != null ? x.RecordedInTodo.VHrAssessed : 0,
            RecordedInTodo_Remuneration = x.RecordedInTodo != null ? x.RecordedInTodo.VHrAssessedCost : 0,
            RecordedForOtherTasks_Mhr = x.RecordedForOtherTasks != null ? x.RecordedForOtherTasks.MHrAssessed : 0,
            RecordedForOtherTasks_Vhr = x.RecordedForOtherTasks != null ? x.RecordedForOtherTasks.VHrAssessed : 0,
            RecordedForOtherTasks_Remuneration = x.RecordedForOtherTasks != null ? x.RecordedForOtherTasks.VHrAssessedCost : 0,

        })));

        return ExcelUtility.ExportExcel(_dataSet);

    }
    public async Task<IEnumerable<WFTaskVHrAnalysisDto>> GetVHrAnalysisDatabyPeriod(string Period, IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {


        if (Filters == null
            || !Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any()
            || !Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any()
            || !Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any()
            )
            throw new EntityServiceException("Range & contact filters are required!");

        DateTime _rangeStart = Convert.ToDateTime(Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Value);
        DateTime _rangeEnd = Convert.ToDateTime(Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Value).AddDays(1);

        var _datalist = new List<WFTaskVHrAnalysisDto>();
        if (Period.Equals("MONTH", StringComparison.OrdinalIgnoreCase))
        {
            var _currentDate = new DateTime(_rangeStart.Year, _rangeStart.Month, 1);
            var _maxDate = new DateTime(_rangeEnd.Year, _rangeEnd.Month, 1);

            while (_currentDate < _maxDate)
            {
                var _periodFilters = Filters.Where(x => !x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)
                          && !x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).ToList();
                _periodFilters.Add(new QueryFilter { Key = "rangeStart", Value = _currentDate.ToLongDateString() });
                _periodFilters.Add(new QueryFilter { Key = "rangeEnd", Value = _currentDate.AddMonths(1).AddDays(-1).ToLongDateString() });

                var _vhrData = await GetVHrAnalysisData(_periodFilters, Search, Sort);
                if (_vhrData.Any())
                {
                    _datalist.Add(_vhrData.First());
                }

                _currentDate = _currentDate.AddMonths(1);
            }
        }

        return _datalist;

    }




    public async Task<int> GetPendingAssessmentCount(int TaskID)
    {

        var _packageTask = await Get()
           .SingleOrDefaultAsync(x => x.ID == TaskID);
        if (_packageTask == null) return 0;



        var _studioTasks = await Get()
            .Include(x => x.Assessments)
             .Where(x => x.IsAssessmentRequired)
            .Where(x => x.StageIndex == 3)
             .Where(x => x.Entity==nameof(Package) && x.EntityID == _packageTask.EntityID)
             .Where(x => x.AssignerContactID == _packageTask.ContactID)
             .Where(x => x.StatusFlag == 1)
             .Where(x => x.OutcomeFlag == 1)
             .Where(x => x.AssessmentRemark!="System Assessed")
             .Where(x => !x.Assessments.Any())
            .CountAsync();

        return _studioTasks;


    }

    public async Task UpdateTaskDue(string Entity, int EntityID)
    {


        var _pendingTasks = await Get()
                         .Where(x => x.Entity==Entity
                                    && x.EntityID == EntityID)
                         .Where(x => x.StatusFlag == 0 || x.StatusFlag == 2 || x.StatusFlag == 3)
                         .ToListAsync();

        foreach (var _task in _pendingTasks)
        {
            var _referenceTask = await GetTaskByStage(Entity, EntityID, _task.WFStageCode);

            _task.MHrAssigned = _referenceTask.MHrAssigned;
            _task.StartDate = _referenceTask.StartDate;
            _task.DueDate = _referenceTask.DueDate;
            //re-calculate if MHrAssigned changed
            _task.VHrAssigned = _task.MHrAssigned * _task.ManValue;
            _task.VHrAssignedCost = _task.VHrAssigned * _task.VHrRate;

            db.Entry(_task).State = EntityState.Modified;


        }
        await db.SaveChangesAsync();

    }


    public async Task<WFTask> GetTaskByStage(string Entity, int EntityID, string StageCode, DateTime? FollowUpDate = null)
    {

        var _stage = await db.WFStages.AsNoTracking()
            .Where(x => x.Code==StageCode)
            .SingleOrDefaultAsync();
        if (_stage == null) throw new EntityServiceException($"Workflow stage not found!");

        dynamic result = null;

        if (Entity == nameof(Project))
        {
            var inquiryService = new ProjectService(db);
            result = await inquiryService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(Todo))
        {
            var todoService = new TodoService(db);
            result = await todoService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(Meeting))
        {
            var meetingService = new MeetingService(db);
            result = await meetingService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(RequestTicket))
        {
            var requestService = new RequestTicketService(db);
            result = await requestService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(Habit))
        {
            var habitService = new HabitService(db);
            result = await habitService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(Expense))
        {
            var expenseService = new ExpenseService(db);
            result = await expenseService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(MeetingAgenda))
        {
            var meetingAgendaService = new MeetingAgendaService(db);
            result = await meetingAgendaService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }
        else if (Entity == nameof(Inspection))
        {
            var inspectionService = new InspectionService(db);
            result = await inspectionService.GetTaskByStage(Entity, EntityID, StageCode, _stage.TaskTitle, _stage.DueDays, FollowUpDate);
        }

        if (result != null)
        {
            return new WFTask
            {
                Title = result.Title,
                Entity = result.Entity,
                EntityID = result.EntityID,
                Subtitle = result.Subtitle,
                WFStageCode = result.WFStageCode,
                StartDate = result.StartDate,
                DueDate = result.DueDate,
                MHrAssigned = result.MHrAssigned,
                IsPreAssignedTimeTask = result.IsPreAssignedTimeTask
            };
        }
        return null;

    }



    public async Task StartFlow(string Entity, int EntityTypeFlag, int EntityID, WFStage? Stage = null)
    {

        //code to end existing flow
        await PurgeTasks(Entity, EntityID);


        var _stage = Stage;
        if (_stage == null)
        {
            //start new flow
            var _query = db.WFStages.AsNoTracking()
                .Include(x => x.Actions)
               .Where(x => x.IsStart
               && x.Entity==Entity);
            var _typeValue = EntityTypeFlag.ToString();
            //check if there is a flow based on Entity.TypeFlag
            if (await _query.Where(x => x.EntityTypeFlag != null).AnyAsync())
            {
                _query = _query.Where(x => x.EntityTypeFlag.Contains(_typeValue));
            }

            _stage = await _query.FirstOrDefaultAsync();
        }

        if (_stage == null)
            return; // throw new CustomException("Workflow start stage not found for "+Entity+" with TypeFlag "+EntityTypeFlag+"!");
        var sharedService = new SharedService(db); ;
        IEnumerable<int> _contactIDs = _stage.IsAssignByRole ?
            await sharedService.GetContactIDByRoleAsync(_stage.Code) :
            await GetAssigneeByEntity(Entity, EntityID, _stage.Code, _stage.AssignByEntityProperty);


        foreach (var _assignee in _contactIDs)
        {
            var _task = await GetTaskByStage(Entity, EntityID, _stage.Code);

            _task.ContactID = _assignee;
            _task.IsPreAssignedTimeTask = _stage.IsPreAssignedTimeTask;
            _task.IsAssessmentRequired = _stage.IsAssessmentRequired;


            try
            {

                await CreateTask(_task, _task.IsPreAssignedTimeTask);
            }
            catch (Exception)
            {

            }


        }

    }

    public async Task CompleteTaskStage(int WFTaskID, bool IsSkipSystemStage = false)
    {
        var _currentTask = await Get()
           .SingleOrDefaultAsync(x => x.ID == WFTaskID);

        if (_currentTask == null || _currentTask.Entity == null || _currentTask.WFStageCode == null)
            throw new Exception("Task not found!");

        var _currentStage = await db.WFStages.AsNoTracking()
            .Include(x => x.Actions)
          .SingleOrDefaultAsync(x => x.Code == _currentTask.WFStageCode);

        if (_currentStage == null)
            return;

        DateTime? _dueDate = _currentStage.ShowFollowUpDate ? _currentTask.FollowUpDate : null;

        var _allTasks = Get()
           .Where(x => x.Entity==_currentTask.Entity
           && x.EntityID == _currentTask.EntityID
           && x.WFStageCode == _currentTask.WFStageCode
           && x.StageRevision == _currentTask.StageRevision);

        var _totalCount = await _allTasks.CountAsync();

        foreach (var action in _currentStage.Actions)
        {
            var _actionCount = _allTasks
           .Where(x => x.StatusFlag == _currentTask.StatusFlag
            && x.OutcomeFlag == action.TaskOutcomeFlag)
           .Count(); //not to consider current task as status is not saved

            var _isStageCompleted = false;
            if (action.ActionByCondition != null
                && action.ActionByCondition.Equals("ALL", StringComparison.OrdinalIgnoreCase))
            {
                _isStageCompleted = _actionCount >= (action.ActionByCount > 0 ? action.ActionByCount : _allTasks.Count());
            }
            else if (action.ActionByCondition == null
                || action.ActionByCondition.Equals("ANY", StringComparison.OrdinalIgnoreCase))
            {
                _isStageCompleted = _actionCount >= (action.ActionByCount > 0 ? action.ActionByCount : 1);
            }

            if (_isStageCompleted)
            {
                await PurgePendingTasks(_currentTask);


                var username = currentUserService.GetCurrentUsername();
                if (!string.IsNullOrEmpty(username))
                {
                    var contact = await db.Contacts.AsNoTracking().FirstOrDefaultAsync(x => x.Username == username);

                    if (contact != null)
                    {
                        var activity = new Activity
                        {
                            ContactID = contact.ID,
                            ContactName = contact.Name,
                            ContactPhotoUrl = contact.PhotoUrl,
                            ContactUID = contact.UID,
                            Entity = _currentTask.Entity,
                            EntityID = _currentTask.EntityID.Value,
                            EntityTitle = _currentTask.Subtitle,
                            Action = _currentTask.Title + " | " + _currentTask.Subtitle + " | " + "R" + _currentTask.StageRevision.ToString(),
                            Status = action.ActivityText,
                            Comments = (_currentTask.ContactID != contact.ID ? "ON BEHALF | " : "") + _currentTask.Comment + (_currentTask.FollowUpDate != null ? " NEXT FOLLOW-UP DATE: " + ClockTools.GetIST(_currentTask.FollowUpDate.Value).ToString("dd MMM yyyy") : "").Trim(),
                            WFTaskID = _currentTask.ID
                        };
                        db.Activities.Add(activity);
                    }
                }

                //start next stage
                if (action.NextStageCode != null && action.NextStageCode != string.Empty)
                    await AssignNextStage(
                        action.NextStageCode,
                        _currentTask.Entity,
                        _currentTask.EntityID.Value,
                        _currentStage.Code,
                        _currentTask.StageRevision,
                        _currentTask.ID,
                         _dueDate,
                         IsSkipSystemStage);

                return;
            }
        }
    }


    private async Task AssignNextStage(
       string WFStageCode,
       string Entity,
        int EntityID,
       string PreviousStageCode,
        int PreviousStageRevision,
        int PreviousTaskID,
        DateTime? PreviousFollowUpDate = null, bool IsSkipSystemStage = false)
    {

        var _nextStage = await db.WFStages.AsNoTracking()
            .Include(x => x.Actions)
           .SingleOrDefaultAsync(x => x.Code==WFStageCode);

        if (_nextStage == null) throw new EntityServiceException($"WFStage {WFStageCode} not found!");

        if (_nextStage.IsSystem)
        {
            if (!IsSkipSystemStage)
                await CompleteWFStage(Entity, EntityID, _nextStage.Code, PreviousTaskID);

            foreach (var action in _nextStage.Actions)
            {
                if (action.NextStageCode != null && action.NextStageCode != string.Empty)
                    await AssignNextStage(
                        action.NextStageCode,
                        Entity,
                        EntityID,
                        PreviousStageCode,
                        PreviousStageRevision,
                        PreviousTaskID,
                        PreviousFollowUpDate);

            }
        }
        else
        {
            var sharedService = new SharedService(db); ;
            var _contacts = _nextStage.IsAssignByRole ?
                           await sharedService.GetContactIDByRoleAsync(_nextStage.Code) :
                           await GetAssigneeByEntity(Entity, EntityID, _nextStage.Code, _nextStage.AssignByEntityProperty);

            foreach (var _assignee in _contacts)
            {
                var _task = await GetTaskByStage(Entity, EntityID, _nextStage.Code, PreviousFollowUpDate);
                _task.ContactID = _assignee;
                _task.IsPreAssignedTimeTask = _nextStage.IsPreAssignedTimeTask;
                _task.IsAssessmentRequired = _nextStage.IsAssessmentRequired;

                await CreateTask(
                  _task,
                  _task.IsPreAssignedTimeTask,
                  false,
                  false,
                  PreviousStageCode,
                  PreviousStageRevision,
                  PreviousTaskID);


            }
        }


    }



    private async Task PurgePendingTasks(WFTask wfTask)
    {

        var _otherPendingTasks = await Get()
            .Where(x => x.Entity==wfTask.Entity
            && x.EntityID == wfTask.EntityID
            && x.WFStageCode == wfTask.WFStageCode
            && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
            .Where(x => x.ID != wfTask.ID)
            .ToListAsync();

        foreach (var _item in _otherPendingTasks)
        {
            _item.Comment = "Un-attended";
            _item.StatusFlag = -1;
            await base.Update(_item);
        }


    }

    public async Task CompleteWFStage(string Entity, int EntityID, string StageCode, int WFTaskID)
    {
        var _wftask = await Get()
     .SingleOrDefaultAsync(x => x.ID == WFTaskID);
        if (_wftask == null) throw new EntityServiceException($"Task not found!");

        if (Entity == nameof(Project))
        {
            var inquiryService = new ProjectService(db);
            await inquiryService.TaskAction(EntityID, StageCode, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else if (Entity == nameof(Meeting))
        {
            var meetingService = new MeetingService(db);
            await meetingService.TaskAction(EntityID, StageCode, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else if (Entity == nameof(Todo))
        {
            var todoService = new TodoService(db);
            await todoService.TaskAction(EntityID, StageCode, _wftask.ID, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else if (Entity == nameof(RequestTicket))
        {
            var requestTicketService = new RequestTicketService(db);
            await requestTicketService.TaskAction(EntityID, StageCode, _wftask.ID, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else if (Entity == nameof(Habit))
        {
            var habitService = new HabitService(db);
            await habitService.TaskAction(EntityID, StageCode, _wftask.ID, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else if (Entity == nameof(Expense))
        {
            var expenseService = new ExpenseService(db);
            await expenseService.TaskAction(EntityID, StageCode, _wftask.ID, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else if (Entity == nameof(Inspection))
        {
            var inspectionService = new InspectionService(db);
            await inspectionService.TaskAction(EntityID, StageCode, _wftask.Comment);
            await db.SaveChangesAsync();
        }
        else
        {

            throw new EntityServiceException($"System stage {StageCode} not found!");
        }


    }


    private async Task<IEnumerable<int>> GetAssigneeByEntity(string entityName, int entityID, string stageCode, string propertyName)
    {
        var sharedService = new SharedService(db); ;
        if (entityName.Equals(nameof(Meeting), StringComparison.OrdinalIgnoreCase))
        {
            var meetingService = new MeetingService(db);
            return await meetingService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        else if (entityName.Equals(nameof(Todo), StringComparison.OrdinalIgnoreCase))
        {
            var todoService = new TodoService(db);
            return await todoService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        else if (entityName.Equals(nameof(RequestTicket), StringComparison.OrdinalIgnoreCase))
        {
            var requestTicketService = new RequestTicketService(db);
            return await requestTicketService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        else if (entityName.Equals(nameof(Habit), StringComparison.OrdinalIgnoreCase))
        {
            var habitService = new HabitService(db);
            return await habitService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        else if (entityName.Equals(nameof(Expense), StringComparison.OrdinalIgnoreCase))
        {
            var expenseService = new ExpenseService(db);
            return await expenseService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        else if (entityName.Equals(nameof(MeetingAgenda), StringComparison.OrdinalIgnoreCase))
        {
            var meetingAgendaService = new MeetingAgendaService(db);
            return await meetingAgendaService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        else if (entityName.Equals(nameof(Inspection), StringComparison.OrdinalIgnoreCase))
        {
            var inspectionService = new InspectionService(db);
            return await inspectionService.GetAssigneeContactIDs(entityID, stageCode, propertyName);
        }
        throw new EntityServiceException($"Task assignee logic for {entityName} not found!");

    }


    public async Task PurgeTasks(string Entity, int EntityID)
    {

        var _tasks = await Get()
       .Where(x => x.Entity==Entity
       && x.EntityID == EntityID
       && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
       .ToListAsync();

        foreach (var item in _tasks)
        {
            await Delete(item.ID);
        }

    }


    public async Task<int> CreateTask(WFTask task, bool UseTime = false, bool AllowMultiple = false, bool AllowPreviousRevision = false, string? PreviousStageCode = null, int? PreviousStageRevision = null, int? PreviousTaskID = null)
    {

        if (task.StageIndex == null) task.StageIndex = 0;

        if (task.StageRevision == 0)
        {

            var _previousTasks = Get()
               .Where(x => x.Entity==task.Entity
               && x.EntityID == task.EntityID && x.ContactID == task.ContactID);

            if (task.WFStageCode != null)
                _previousTasks = _previousTasks.Where(x => x.WFStageCode == task.WFStageCode);
            else
                _previousTasks = _previousTasks.Where(x => x.StageIndex == task.StageIndex && x.StageIndex != 3);

            task.StageRevision = await _previousTasks.AnyAsync()
                ? await _previousTasks.MaxAsync(x => x.StageRevision) + 1
                : 0;

        }

        await ValidateTask(task, false, AllowPreviousRevision, AllowMultiple);

        if (task.Title == null)
        {
            if (task.WFStageCode != null)
            {
                var _stage = await db.WFStages.AsNoTracking()
               .SingleOrDefaultAsync(x => x.Code == task.WFStageCode);
                if (_stage != null)
                    task.Title = _stage.TaskTitle;
            }
            else
            {
                throw new EntityServiceException("Task Title not found! Please try again.");
            }

        }

        if (task.StatusFlag == 0)
        {
            if (!task.IsPreAssignedTimeTask)
            {
                //Entity.DueDate = ClockTools.GetNextValidWorkingDate(Entity.DueDate,
                //        await IsEvenSaturdayOff(),
                //        await IsOddSaturdayOff(),
                //        await GetHolidays());

                //Entity.StartDate = DateTime.UtcNow;
                //Entity.StartDate = ClockTools.GetNextValidWorkingDate(Entity.StartDate,
                //        await IsEvenSaturdayOff(),
                //        await IsOddSaturdayOff(),
                //        await GetHolidays());

            }

        }

        task.PreviousStageCode = PreviousStageCode;
        task.PreviousStageRevision = PreviousStageRevision;
        task.PreviousTaskID = PreviousTaskID;
        task.ManValue = 1;
        task.CompanyID = 1;
        var sharedService = new SharedService(db);
        var _appointment = await sharedService.GetLastAppointment(task.ContactID);
        if (_appointment != null)
        {
            task.ManValue = _appointment.ManValue;
            task.CompanyID = _appointment.CompanyID;
        }

        try
        {
            task.VHrRate = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));
        }
        catch
        {
            task.VHrRate = 0;
        }
        //Entity.MHrAssigned = Entity.MHrAssigned==0 && Entity.IsPreAssignedTimeTask ? (Entity.DueDate - Entity.StartDate).TotalHours : 0;

        task.VHrAssigned = task.MHrAssigned * task.ManValue;
        task.VHrAssignedCost = task.VHrAssigned * task.VHrRate;

        if (task.StatusFlag == 1)
        {
            task.VHrConsumed = task.MHrConsumed * task.ManValue;
            task.VHrAssessed = task.MHrAssessed * task.ManValue;
            task.VHrConsumedCost = task.VHrConsumed * task.VHrRate;
            task.VHrAssessedCost = task.VHrAssessed * task.VHrRate;
        }

        //if (Entity.StartDate > Entity.DueDate) throw new CustomException("Task start & end is invalid! Please select again!");
        //This logic fails for MEETING and other tasks

        await base.Create(task);

        if (task.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
        {
            var contact = await db.Contacts.AsNoTracking().FirstOrDefaultAsync(x => x.ID == task.ContactID);
            if (contact != null && contact.Username != null)
            {
                await sharedService.PushNotification(contact.Username,
                    $"New {task.Entity} Task Assigned",
                    $"{task.Title} | {task.Subtitle} | {ClockTools.GetIST(task.DueDate).ToString("dd MMM yyyy HH:mm")}",
                    nameof(WFTask), task.ID.ToString()
                );
            }

        }


        return task.ID;

    }

    public async Task ValidateTask(WFTask Entity, bool isUpdate = false, bool AllowPreviousRevision = false, bool AllowMultiple = false)
    {

        if (Entity.WFStageCode == null) return;

        var _contact = await db.Contacts.AsNoTracking()
                .Where(x => x.Username != null)
               .Where(x => x.ID == Entity.ContactID).SingleOrDefaultAsync();

        if (_contact == null) throw new EntityServiceException("Assignee not found! Please try again.");


        if (!isUpdate && !AllowMultiple)
        {
            var _query = Get()
               .Where(x => x.ID != Entity.ID)
                   .Where(x => x.ContactID == Entity.ContactID && x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED
                   && x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_UNATTENDED);

            _query = _query
                .Where(x => x.Entity == Entity.Entity
                        && x.EntityID == Entity.EntityID
                       && x.WFStageCode == Entity.WFStageCode);

            if (!AllowPreviousRevision)
            {

                _query = _query
              .Where(x => x.StageRevision <= Entity.StageRevision);
            }

            if (await _query.AnyAsync())
            {
                var _first = await _query.FirstOrDefaultAsync();

                throw new EntityServiceException($"Task already exists! \n{_first.Title} | {_first.Subtitle} \n [{ClockTools.GetIST(_first.StartDate).ToString("dd MMM yyyy HH:mm")}-{ClockTools.GetIST(_first.DueDate).ToString("dd MMM yyyy HH:mm")}]");
            };
        }


    }

    public async Task PauseOtherActiveTasks(int TaskID, int ContactID)
    {
        try
        {
            var _activeTasks = await Get()
    .Where(x => x.ContactID == ContactID
    && x.ID != TaskID
    && x.StatusFlag == 2)
    .ToListAsync();

            foreach (var item in _activeTasks)
            {
                item.OutcomeFlag = 1;
                item.StatusFlag = 3;
                var _history = "Paused [" + ClockTools.GetISTNow().ToString("dd MMM yyyy HH:mm") + "]";
                item.History = item.History != null ? item.History + " " + _history : _history;
                db.Entry(item).State = EntityState.Modified;
                await db.SaveChangesAsync();

                if (item.WFStageCode != null)
                {

                    try
                    {
                        await CompleteTaskStage(item.ID);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Other active tasks could not be paused! Please try again." + "\n" + ex.Message);
                    }

                }
                else
                {


                    if (item.StatusFlag == 2) //STARTED
                    {
                        var timeEntryService = new TimeEntryService(db);
                        await timeEntryService.StartTimeLog(new TimeEntry
                        {
                            ContactID = item.ContactID,
                            WFTaskID = item.ID,
                            Entity = item.Entity,
                            EntityID = item.EntityID,
                            EntityTitle = item.Subtitle,
                            TaskTitle = item.Title,
                        });
                    }
                    else if (item.StatusFlag == 3) //PAUSED
                    {
                        var timeEntryService = new TimeEntryService(db);
                        await timeEntryService.EndTimeLog(item.ID, true);
                    }
                    else
                    {
                        var timeEntryService = new TimeEntryService(db);
                        await timeEntryService.EndTimeLog(item.ID);
                    }


                }
            }


        }
        catch (Exception)
        {
            throw new EntityServiceException("Other active tasks could not be paused! Please try again.");
        }
    }

    public async Task<bool> IsAllTasksCompleted(WFTask obj, int RequiredCount = 0)
    {

        IQueryable<WFTask> _tasks = Get();

        if (obj.EntityID != null)
        {
            _tasks = _tasks
                            .Where(x => x.Entity==obj.Entity && x.EntityID == obj.EntityID);
        }
        else
        {
            return false;
        }

        _tasks = _tasks.Where(x => x.StageIndex == obj.StageIndex
               && x.StageRevision == obj.StageRevision);

        if (RequiredCount == 0)
        {
            RequiredCount = await _tasks
            .CountAsync();
        }

        var _count = await _tasks
            .Where(x => x.StatusFlag == 1)
            .CountAsync();

        if (_count >= RequiredCount) return true;

        return false;

    }

    public async Task<bool> IsAllApproved(WFTask obj, int RequiredCount = 0)
    {

        IQueryable<WFTask> _tasks = Get();

        if (obj.EntityID != null)
        {
            _tasks = _tasks
                            .Where(x => x.Entity==obj.Entity && x.EntityID == obj.EntityID);
        }
        else
        {
            return false;
        }

        _tasks = _tasks.Where(x => x.StageIndex == obj.StageIndex
               && x.StageRevision == obj.StageRevision);

        if (RequiredCount == 0)
        {
            RequiredCount = await _tasks
                                .CountAsync();
        }

        var _count = await _tasks
                            .Where(x => x.StatusFlag == 1 && x.OutcomeFlag == 1)
                            .CountAsync();

        if (_count >= RequiredCount) return true;

        return false;

    }

    public async Task HandleParallelTasks(int CurrentTaskID)
    {

        var _task = await Get().Where(x => x.ID == CurrentTaskID).SingleOrDefaultAsync();
        if (_task == null) return;

        var _pending = await Get()
                                   .Where(x => x.ID != CurrentTaskID
                                                           && x.StatusFlag == 0
                                                           && x.Entity==_task.Entity
                                                           && x.EntityID == _task.EntityID
                                                           && x.StageIndex == _task.StageIndex
                                                           && x.WFStageCode == x.WFStageCode)
                                                               .ToListAsync();

        foreach (var _item in _pending)
        {
            _item.Comment = "Un-attended";
            _item.StatusFlag = -1;
            db.Entry(_item).State = EntityState.Modified;
        }

        await db.SaveChangesAsync();

    }

    public async Task<IEnumerable<WFTaskVHrAnalysisDto>> GetVHrAnalysisData(IEnumerable<QueryFilter> Filters = null, string Search = null, string Sort = null)
    {

        if (Filters == null
            || !Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any()
            || !Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any()
            )
            throw new EntityServiceException("Range filters are required!");

        DateTime _rangeStart = Convert.ToDateTime(Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Value);
        DateTime _rangeEnd = Convert.ToDateTime(Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Value).AddDays(1);


        var _expectedMhr = 190;

        var _currentVHrRate = 0.0m;
        try
        {
            var sharedService = new SharedService(db); ;
            _currentVHrRate = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));
        }
        catch
        {

        }

        var contactQuery = db.Contacts.AsNoTracking()

            .Include(x => x.Appointments).ThenInclude(x => x.Company)
            .Where(x => x.Appointments.Any(c => !c.IsDeleted && c.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED));

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Contact>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ID == isNumeric);
                }
                contactQuery = contactQuery.Where(predicate);
            }
        }

        if (Search != null && Search != "")
        {
            contactQuery = contactQuery.Where(s => (s.FirstName + " " + s.LastName).ToLower().Contains(Search.ToLower()));
        }

        var appointmentTypeMasters=await db.TypeMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .Select(x => new
            {
                x.Value,
                x.Title
            })
            .ToListAsync();

        var contacts=await contactQuery
            .ToListAsync();

        var _filteredContacts = contacts
            .SelectMany(x=>x.Appointments.Where(a =>  a.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED),(a,b) => new
             {
                 ContactID = a.ID,
                 Person = a.FirstName + " " + a.LastName,
                 CurrentManValue = b.ManValue,
                 Company=b.Company.Initials,
                 AppointmentType= appointmentTypeMasters.Where(c => c.Value == b.TypeFlag).Any() ? appointmentTypeMasters.Where(c => c.Value == b.TypeFlag).FirstOrDefault().Title : "NA",
            })
            .ToList();


        var _contactFilters = _filteredContacts
            .Select(x => new QueryFilter
            {
                Key = "ContactID",
                Value = x.ContactID.ToString()
            }).ToList();

        var filteredContactIDs = _filteredContacts.Select(x => x.ContactID).ToList();

        var taskQuery = db.WFTasks.AsNoTracking()

            .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
              .Where(x => x.Entity != null && x.EntityID != null)
              .Where(x => filteredContactIDs.Any(c => c == x.ContactID))
              .Where(x => x.VHrAssessed > 0)
              .Where(x => x.CompletedDate != null && x.CompletedDate >= _rangeStart && x.CompletedDate < _rangeEnd);

        var taskData = await taskQuery
            .Select(x => new
            {
                x.ID,
                x.ContactID,
                x.Entity,
                x.EntityID,
                x.MHrAssessed,
                x.VHrAssessed,
                x.VHrAssessedCost
            })
            .ToListAsync();


        var packageQuery = db.Packages.AsNoTracking()
                           .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
                           .Where(x => x.SubmissionDate != null && x.SubmissionDate.Value >= _rangeStart && x.SubmissionDate.Value < _rangeEnd)
                           .Where(x => x.VHrAssigned > x.VHrConsumed)
                           .Include(x => x.Associations);

        var packageData = await packageQuery
            .Select(x => new
            {
                x.ProjectID,
                x.ID,
                x.VHrAssigned,
                x.VHrAssignedCost,
                x.VHrConsumed,
                x.VHrConsumedCost,
                Associations = x.Associations.Where(a => !a.IsDeleted).Where(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER || a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE)
            })
            .ToListAsync();

        var filteredPackageIDs = packageData.Select(x => x.ID).ToList();

        //var packageAssociationQuery = db.PackageAssociations.AsNoTracking()
        //
        //.Where(x => filteredPackageIDs.Any(p => p == x.PackageID))
        //.Where(x => filteredContactIDs.Any(c => c == x.ContactID));

        //var packageAssociatesData = await packageAssociationQuery
        //    .Select(x => new
        //    {
        //        x.ContactID,
        //        x.PackageID,
        //        x.TypeFlag
        //    })
        //    .ToListAsync();

        //var packageAssociatesData = packageData.SelectMany(x => x.Associations, (a, b) => new
        //{
        //    b.ContactID,
        //    b.PackageID,
        //    b.TypeFlag
        //}).ToList();

        var _earnedInSelfPackages = packageData.SelectMany(x => x.Associations.Where(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE), (a, b) => new
        {
            b.ContactID,
            b.PackageID,
            a.VHrAssigned,
            a.VHrAssignedCost,
            a.VHrConsumed,
            a.VHrConsumedCost,
            VHrAssessed = a.VHrAssigned - a.VHrConsumed,
            VHrAssessedCost = a.VHrAssignedCost - a.VHrConsumedCost,
        })
            .GroupBy(x => x.ContactID)
                .Select(x => new WFTaskVHrDto
                {
                    ContactID = x.Key,
                    MHrAssessed = 0,
                    VHrAssessed = x.Sum(a => a.VHrAssessed),
                    VHrAssessedCost = x.Sum(a => a.VHrAssessedCost),
                })
            .ToList();


        //var _earnedInSelfPackages = packageAssociatesData
        //    .Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE)
        //    .Select(x => new
        //    {
        //        x.ContactID,
        //        x.PackageID,
        //        VHrAssigned = packageData.Where(p => p.ID == x.PackageID).Any() ?
        //                      packageData.Where(p => p.ID == x.PackageID).FirstOrDefault().VHrAssigned :
        //                      0,
        //        VHrAssignedCost = packageData.Where(p => p.ID == x.PackageID).Any() ?
        //                      packageData.Where(p => p.ID == x.PackageID).FirstOrDefault().VHrAssignedCost :
        //                      0,
        //        VHrConsumed = packageData.Where(p => p.ID == x.PackageID).Any() ?
        //                      packageData.Where(p => p.ID == x.PackageID).FirstOrDefault().VHrConsumed :
        //                      0,
        //        VHrConsumedCost = packageData.Where(p => p.ID == x.PackageID).Any() ?
        //                      packageData.Where(p => p.ID == x.PackageID).FirstOrDefault().VHrConsumedCost :
        //                      0,
        //    })
        //     .Select(x => new
        //     {
        //         x.ContactID,
        //         VHrAssessed = x.VHrAssigned - x.VHrConsumed,
        //         VHrAssessedCost = x.VHrAssignedCost - x.VHrConsumedCost,
        //     })
        //        .GroupBy(x => x.ContactID)
        //        .Select(x => new WFTaskVHrDto
        //        {
        //            ContactID = x.Key,
        //            MHrAssessed = 0,
        //            VHrAssessed = x.Sum(a => a.VHrAssessed),
        //            VHrAssessedCost = x.Sum(a => a.VHrAssessedCost),
        //        })
        //    .ToList();


        //  var _ATasks = packageAssociatesData
        //      .Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE)
        //.GroupJoin(taskData.Where(x => x.Entity == nameof(Package)),
        //aso => new { EntityID = aso.PackageID, aso.ContactID },
        // task => new { EntityID = task.EntityID.Value, task.ContactID },
        //(aso, tasks) => new { aso, tasks = tasks.DefaultIfEmpty() })
        //.SelectMany(x => x.tasks.DefaultIfEmpty(),
        //(x, y) => new { x.aso, task = y }).Where(x => x.task != null)
        //.Select(x => x.task).ToList();

        //  var _PTasks = packageAssociatesData
        //      .Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER)
        //  .GroupJoin(taskData.Where(x => x.Entity == nameof(Package)),
        //  aso => new { EntityID = aso.PackageID, aso.ContactID },
        //   task => new { EntityID = task.EntityID.Value, task.ContactID },
        //  (aso, tasks) => new { aso, tasks = tasks.DefaultIfEmpty() })
        //  .SelectMany(x => x.tasks.DefaultIfEmpty(),
        //  (x, y) => new { x.aso, task = y }).Where(x => x.task != null)
        //  .Select(x => x.task).ToList();

        var _recordedInOtherPackages = taskData
            .Where(x => x.Entity == nameof(Package))
          //.Where(x => !_ATasks.Where(t => t.ID == x.ID).Any())
          //.Where(x => !_PTasks.Where(t => t.ID == x.ID).Any())

          .GroupBy(x => x.ContactID)
          .Select(x => new WFTaskVHrDto
          {
              ContactID = x.Key,
              MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
              VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
              VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
          })
               .ToList();


        var projectTodoIDs = await db.Todos.AsNoTracking()

            .Where(x => x.ProjectID != null)
            .Select(x => x.ID)
            .ToListAsync();


        var _projectTodoTaskVHr = taskData
           .Where(x => x.Entity == nameof(Todo))
           .Where(x => projectTodoIDs.Any(c => c == x.EntityID))
            .Select(x => new
            {
                x.ContactID,
                x.MHrAssessed,
                x.VHrAssessed,
                x.VHrAssessedCost,
            })
                .GroupBy(x => x.ContactID)
                  .Select(x => new WFTaskVHrDto
                  {
                      ContactID = x.Key,
                      MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                      VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                      VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                  })
                   .ToList();

        var _nonProjectTodoTaskVHr = taskData
          .Where(x => x.Entity == nameof(Todo))
          .Where(x => !projectTodoIDs.Any(c => c == x.EntityID))
           .Select(x => new
           {
               x.ContactID,
               x.MHrAssessed,
               x.VHrAssessed,
               x.VHrAssessedCost,
           })
               .GroupBy(x => x.ContactID)
                 .Select(x => new WFTaskVHrDto
                 {
                     ContactID = x.Key,
                     MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                     VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                     VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                 })
                  .ToList();


        var _meetingTaskVHr = taskData
           .Where(x => x.Entity == nameof(Meeting))
            .Select(x => new
            {
                x.ContactID,
                x.MHrAssessed,
                x.VHrAssessed,
                x.VHrAssessedCost,
            })
                .GroupBy(x => x.ContactID)
                  .Select(x => new WFTaskVHrDto
                  {
                      ContactID = x.Key,
                      MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                      VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                      VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                  })
                   .ToList();


        var _inspectionTaskVHr = taskData
           .Where(x => x.Entity == nameof(Inspection))
            .Select(x => new
            {
                x.ContactID,
                x.MHrAssessed,
                x.VHrAssessed,
                x.VHrAssessedCost,
            })
                .GroupBy(x => x.ContactID)
                  .Select(x => new WFTaskVHrDto
                  {
                      ContactID = x.Key,
                      MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                      VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                      VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                  })
                   .ToList();



        var _inquiryTaskVHr = taskData
           .Where(x => x.Entity == nameof(Project))
            .Select(x => new
            {
                x.ContactID,
                x.MHrAssessed,
                x.VHrAssessed,
                x.VHrAssessedCost,
            })
                .GroupBy(x => x.ContactID)
                  .Select(x => new WFTaskVHrDto
                  {
                      ContactID = x.Key,
                      MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                      VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                      VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                  })
                   .ToList();

        var _otherTaskVHr = taskData
          .Where(x => x.Entity != nameof(Project) && x.Entity != nameof(Meeting) && x.Entity != nameof(Inspection) && x.Entity != nameof(Todo) && x.Entity != nameof(Package))
           .Select(x => new
           {
               x.ContactID,
               x.MHrAssessed,
               x.VHrAssessed,
               x.VHrAssessedCost,
           })
               .GroupBy(x => x.ContactID)
                 .Select(x => new WFTaskVHrDto
                 {
                     ContactID = x.Key,
                     MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                     VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                     VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                 })
                  .ToList();



        return _filteredContacts
            .Select(x => new WFTaskVHrAnalysisDto
            {
                FromDate = _rangeStart,
                ToDate = _rangeEnd.AddDays(-1),
                ContactID = x.ContactID,
                Person = x.Person,
                Company = x.Company,
                RenumerationType = x.AppointmentType,
                CurrentManValue = x.CurrentManValue,
                CurrentVHrRate = _currentVHrRate,
                ExpectedMHr = _expectedMhr,
                EarnedInSelfPackages = _earnedInSelfPackages.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedInOthersPackages = _recordedInOtherPackages.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedForMeetings = _meetingTaskVHr.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedForInspections = _inspectionTaskVHr.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedInProjectTodo = _projectTodoTaskVHr.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedInTodo = _nonProjectTodoTaskVHr.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedForInquiries = _inquiryTaskVHr.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),
                RecordedForOtherTasks = _otherTaskVHr.Where(a => a.ContactID == x.ContactID).FirstOrDefault(),

            })
            //.Where(x => x.VHr > 0)
            .OrderBy(x => x.Person);


    }

    public async Task<IEnumerable<WFTaskVHrDto>> GetTaskVHrByEntity(string EntityName, IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
        var _tasks = Get()
          .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
                .Where(x => x.Entity != null && x.EntityID != null)
                ;

        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _tasks = _tasks.Where(x => x.CompletedDate != null && x.CompletedDate.Value >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value).AddDays(1);

                _tasks = _tasks.Where(x => x.CompletedDate != null && x.CompletedDate.Value < result);

            }
            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<WFTask>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _tasks = _tasks.Where(predicate);
            }
        }


        if (Search != null && Search != "")
        {
            _tasks = _tasks.Where(x => (x.Contact.FirstName + " " + x.Contact.LastName).ToLower().Contains(Search.ToLower())

                              );

        }

        return await _tasks
            .Where(x => x.Entity==EntityName)
            .Select(x => new
            {
                x.ContactID,
                x.MHrAssessed,
                x.VHrAssessed,
                x.VHrAssessedCost,

            })
            .GroupBy(x => x.ContactID)
                   .Select(x => new WFTaskVHrDto
                   {
                       ContactID = x.Key,
                       MHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.MHrAssessed) : 0,
                       VHrAssessed = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessed) : 0,
                       VHrAssessedCost = x.Any(t => t != null) ? x.Sum(t => t.VHrAssessedCost) : 0,
                   })
            .ToListAsync();


    }

    public async Task AssignLeaveTasks(int LeaveID)
    {

        var _leave = await db.Leaves.AsNoTracking()
                            .SingleOrDefaultAsync(x => x.ID == LeaveID);

        if (_leave == null) return;

        var _applicant = await db.Contacts.AsNoTracking()

                        .SingleOrDefaultAsync(x => x.ID == _leave.ContactID);




        var _type = await db.TypeMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(Leave) && x.Value == _leave.TypeFlag).AnyAsync() ? (await db.TypeMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(Leave) && x.Value == _leave.TypeFlag)
            .FirstOrDefaultAsync()).Title : "Leave";

        var _subtitle = _applicant.FullName + " | " + ClockTools.GetIST(_leave.Start).ToString("dd MMM yyyy") + " - " + ClockTools.GetIST(_leave.End).ToString("dd MMM yyyy");
        if (_leave.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK || _leave.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY)
        {
            _subtitle = _applicant.FullName + " | " + ClockTools.GetIST(_leave.Start).ToString("dd MMM yyyy HH:mm") + " - " + ClockTools.GetIST(_leave.End).ToString("HH:mm");
        }
        var sharedService = new SharedService(db); ;
        var _dueDate = DateTime.UtcNow.AddDays(1).Date
                    .AddMinutes(await sharedService.GetBusinessEndMinutesIST()) < _leave.End ? DateTime.UtcNow.AddDays(1).Date
                    .AddMinutes(await sharedService.GetBusinessEndMinutesIST()) :
                    _leave.End;

        var _hr = await sharedService.GetContactIDByRoleAsync(McvConstant.PERMISSION_LEAVE_SPECIAL_EDIT);

        var _masters = await sharedService.GetContactIDByRoleAsync(McvConstant.PERMISSION_MASTER);

        if (_hr.Any(x => x == _applicant.ID))
        {




            foreach (var _emp in _masters)
            {
                var _TLTask = new WFTask
                {
                    ContactID = _emp,
                    Title = _type + " Application",
                    Subtitle = _subtitle,
                    StageIndex = 1,

                    OutcomeFlag = 0,
                    Entity = nameof(Leave),
                    EntityID = LeaveID,
                    StartDate = DateTime.UtcNow,
                    DueDate = _dueDate
                };

                var _result = await CreateTask(_TLTask);

            }

        }
        else
        {


            foreach (var _emp in _hr)
            {
                var _TLTask = new WFTask
                {
                    ContactID = _emp,
                    Title = _type + " Application",
                    Subtitle = _subtitle,
                    StageIndex = 1,

                    OutcomeFlag = 0,
                    Entity = nameof(Leave),
                    EntityID = LeaveID,
                    StartDate = DateTime.UtcNow,
                    DueDate = _dueDate
                };

                var _result = await CreateTask(_TLTask);

            }
        }


    }

    public async Task CompletePackageTasks(int WFTaskID, string taskStatus)
    {

        var sharedService = new SharedService(db);
        var task = await db.WFTasks.AsNoTracking()
            .Include(x => x.Contact)
                .Where(x => x.ID == WFTaskID)
                .SingleOrDefaultAsync();

        if (task == null) throw new EntityServiceException("Task not found");

        if (task.StageIndex == 1) //Initial Review
        {
            await PauseOtherActiveTasks(task.ID, task.ContactID);

            if (task.StatusFlag == 2) //STARTED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.StartTimeLog(new TimeEntry
                {
                    ContactID = task.ContactID,
                    WFTaskID = task.ID,
                    Entity = task.Entity,
                    EntityID = task.EntityID,
                    EntityTitle = task.Subtitle,
                    TaskTitle = task.Title,
                });
            }
            else if (task.StatusFlag == 3) //PAUSED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID, true);
            }
            else
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID);
            }

            if (task.StatusFlag == 1)
            {
                //check for ALL-WIN
                var isAllDone = await IsAllTasksCompleted(task);
                //var isAllApproved = await taskService.IsAllApproved(_task);

                //next stage
                if (isAllDone)
                {
                    //await taskService.HandleParallelTasks(_task.ID);

                    await db.SaveChangesAsync();

                    var packageService = new PackageService(db);
                    await packageService.PurgeInvitees(task.EntityID.Value);

                    await AssignPackageTask(task.EntityID.Value, 5);
                }
            }
        }
        else if (task.StageIndex == 3) //Studio
        {
            //check for any other tasks inprogress n pause them
            await PauseOtherActiveTasks(task.ID, task.ContactID);

            if (task.StatusFlag == 2) //STARTED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.StartTimeLog(new TimeEntry
                {
                    ContactID = task.ContactID,
                    WFTaskID = task.ID,
                    Entity = task.Entity,
                    EntityID = task.EntityID,
                    EntityTitle = task.Subtitle,
                    TaskTitle = task.Title,
                });
            }
            else if (task.StatusFlag == 3) //PAUSED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID, true);
            }
            else
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID);

                //Notify Assigner
                if (task.AssignerContactID != null)
                {

                    var contact = await db.Contacts.AsNoTracking().FirstOrDefaultAsync(x => x.ID == task.AssignerContactID);
                    if (contact != null && contact.Username != null)
                    {
                        await sharedService.PushNotification(contact.Username, $"Studio Work Completed by {task.Contact.Name}", $"{task.Title} | {task.Subtitle} | {ClockTools.GetIST(task.DueDate).ToString("dd MMM yyyy HH:mm")}", nameof(WFTask), task.ID.ToString());
                    }


                }
            }
        }
        else if (task.StageIndex == 5)//Package
        {
            var _studioWorks = db.WFTasks.AsNoTracking()
           .Include(x => x.Assessments)
           .Where(x => x.StageIndex == 3 && x.OutcomeFlag != -1
           && x.Entity==task.Entity
           && x.EntityID == task.EntityID);

            var _pendingStudioWorks = _studioWorks
                .Where(x => x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED);

            var _unAssessedStudioWorks = _studioWorks
               .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED
               && !x.Assessments.Any()
               && x.IsAssessmentRequired);

            var _assignerPendinStudioWorks = _pendingStudioWorks;
            //.Where(x => x.AssignerContactID == _task.ContactID);

            var _assignerUnAssessedStudioWorks = _unAssessedStudioWorks;
            //.Where(x => x.AssignerContactID == _task.ContactID);

            if (await _assignerPendinStudioWorks.AnyAsync())
                throw new EntityServiceException("Studio Work tasks are still in progress!");

            if (await _assignerUnAssessedStudioWorks.AnyAsync())
                throw new EntityServiceException("Studio Work tasks assessment not done!");


            if (!await _pendingStudioWorks.AnyAsync() && !await _unAssessedStudioWorks.AnyAsync())
            {
                await HandleParallelTasks(task.ID);
                await AssignPackageTask(task.EntityID.Value, 6);

                var _otherTaskContactIDs = await db.WFTasks.AsNoTracking()
                    .Where(x => x.Entity==task.Entity && x.EntityID == task.EntityID)
                    .Where(x => x.StageIndex == task.StageIndex
                   && x.StageRevision == task.StageRevision).Select(x => x.ContactID).Distinct().ToListAsync();


            }
        }
        else if (task.StageIndex == 6)//Final Review Meet
        {
            if (task.OutcomeFlag == 0)
                throw new EntityServiceException("Task outcome not updated");

            if (task.OutcomeFlag == -1) //rejected
            {
                var _nextRevision = task.StageRevision + 1;
                await HandleParallelTasks(task.ID);
                await AssignPackageTask(task.EntityID.Value, 5);
            }
            else
            {
                //check for ALL-WIN
                var isAllDone = await IsAllTasksCompleted(task);
                var isAllApproved = await IsAllApproved(task);

                //next stage
                if (isAllDone && isAllApproved)
                {
                    var packageService = new PackageService(db);
                    await packageService.PurgeInvitees(task.EntityID.Value);
                    await AssignPackageTask(task.EntityID.Value, 7);
                }
            }
        }
        else if (task.StageIndex == 7)//submission
        {
            var packageService = new PackageService(db);
            await packageService.ProcessSubmission(task.EntityID.Value);
            await HandleParallelTasks(task.ID);

            await packageService.PurgeInvitees(task.EntityID.Value);
        }
       var username = currentUserService.GetCurrentUsername();
                if (!string.IsNullOrEmpty(username))
                {
                    var contact = await db.Contacts.AsNoTracking().FirstOrDefaultAsync(x => x.Username == username);

                    if (contact != null)
                    {
                        var activity = new Activity
                        {
                            ContactID = contact.ID,
                            ContactName = contact.Name,
                            ContactPhotoUrl = contact.PhotoUrl,
                            ContactUID = contact.UID,
                            Entity = task.Entity,
                            EntityID = task.EntityID.Value,
                            EntityTitle = task.Subtitle,
                            Action = task.Title + " | " + task.Subtitle + " | " + "R" + task.StageRevision.ToString(),
                            Status = taskStatus,
                            Comments = (task.ContactID != contact.ID ? "ON BEHALF | " : "") + task.Comment + (task.FollowUpDate != null ? " NEXT FOLLOW-UP DATE: " + ClockTools.GetIST(task.FollowUpDate.Value).ToString("dd MMM yyyy") : "").Trim(),
                            WFTaskID = task.ID
                        };
                        db.Activities.Add(activity);
                    }
                }

    }

    public async Task<string> AssignPackageTask(int PackageID, int StageIndex = 1, bool IsAttached = false)
    {

        var _package = await db.Packages.AsNoTracking()

            .Where(x => x.ID == PackageID).SingleOrDefaultAsync();

        if (_package == null) throw new EntityServiceException("Package not found");

        var _packageAssociations = await db.PackageAssociations.AsNoTracking()
            .Where(x => x.PackageID == _package.ID).ToListAsync();

        var _project = await db.Projects
            .AsNoTracking()
            .Include(x => x.Associations)
            .SingleOrDefaultAsync(x => x.ID == _package.ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        if (!_project.Associations.Where(x => x.TypeFlag == 0).Any())
            throw new EntityServiceException("Project partner not found!");



        var _title = String.Empty;

        switch (StageIndex)
        {
            case 1:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_1}";
                break;

            case 3:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_3}";
                break;

            case 5:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_5}";
                break;

            case 6:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_6}";
                break;

            case 7:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_7}";
                break;
        }

        _package.ActiveStage = _title;

        var _assigneeContactIDs = new List<int>();

        if (StageIndex == 1)
        {

            if (_packageAssociations.Any())
            {
                foreach (var obj in _packageAssociations)
                {
                    var _lastTasks = await db.WFTasks.AsNoTracking()
                             .Where(x => x.Entity==nameof(Package)
                          && x.EntityID == _package.ID
                          && x.StageIndex == StageIndex)
                             .Where(x => x.ContactID == obj.ContactID).AnyAsync();

                    if (!_lastTasks && !_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }

        }
        else if (StageIndex == 5 || StageIndex == 7)
        {
            if (_packageAssociations.Where(x => x.TypeFlag == 0 || x.TypeFlag == 1).Any())
            {
                foreach (var obj in _packageAssociations.Where(x => x.TypeFlag == 0 || x.TypeFlag == 1))
                {
                    if (!_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }
        }
        else if (StageIndex == 6)
        {
            if (_packageAssociations.Where(x => x.TypeFlag == 0).Any())
            {
                foreach (var obj in _packageAssociations.Where(x => x.TypeFlag == 0))
                {
                    if (!_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }
            if (_packageAssociations.Where(x => x.TypeFlag == 2).Any())
            {
                foreach (var obj in _packageAssociations.Where(x => x.TypeFlag == 2))
                {
                    if (!_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }
        }

        var packageService = new PackageService(db);
        var _taskEnd = await packageService.GetPackageTaskDueDate(_package.StartDate, _package.FinalDate, StageIndex);


        foreach (var id in _assigneeContactIDs)
        {
            var _lastTask = await db.WFTasks.AsNoTracking()
          .Where(x => x.Entity==nameof(Package)
       && x.EntityID == _package.ID
       && x.StageIndex == StageIndex
       && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
          .Where(x => x.ContactID == id)
          .OrderByDescending(x => x.Created)
          .FirstOrDefaultAsync();

            var _nextRevision = 0;
            if (_lastTask != null)
                _nextRevision = _lastTask.StageRevision + 1;

            var _task = new WFTask
            {
                ContactID = id,
                Entity = nameof(Package),
                EntityID = _package.ID,
                StartDate = DateTime.UtcNow,
                DueDate = _taskEnd,
                Title = _title,
                Subtitle = _project.Title + " | " + _package.Title,
                StageIndex = StageIndex,
                StageRevision = _nextRevision,
            };

            await CreateTask(_task);

        }


        if (!IsAttached)
            db.Entry(_package).State = EntityState.Modified;
        await db.SaveChangesAsync();

        return _package.ActiveStage;

    }

    public async Task StartPackageFlow(int packageID)
    {
        var package = await db.Packages.AsNoTracking()

               .SingleOrDefaultAsync(x => x.ID == packageID);

        if (package == null) throw new EntityServiceException("Package not found!");

        if (package.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
        {
            package.ActiveStage = await AssignPackageTask(package.ID, 1, true);

            db.Entry(package).State = EntityState.Modified;
            await db.SaveChangesAsync();
        }
    }

    public async Task UpdatePackageTaskDue(int ID, DateTime StartDate, DateTime FinalDate)
    {

        var _tasks = await Get()
          .Where(x => x.Entity == nameof(Package) && x.EntityID == ID && x.StatusFlag != 1 && x.StatusFlag != -1 && x.StageIndex != 3)
          .ToListAsync();

        var packageService = new PackageService(db);
        foreach (var _task in _tasks)
        {
            _task.DueDate = await packageService.GetPackageTaskDueDate(StartDate, FinalDate, _task.StageIndex.Value);
        }
        await db.SaveChangesAsync();


    }
}

public class WFTaskVHrDto
{
    public int ContactID { get; set; }
    public string Person { get; set; }
    public string Entity { get; set; }
    public decimal MHrAssessed { get; set; } = 0;
    public decimal VHrAssessed { get; set; } = 0;
    public decimal VHrAssessedCost { get; set; } = 0;
}

public class WFTaskVHrAnalysisDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int ContactID { get; set; }
    public string Person { get; set; }
    public string Company { get; set; }
    public string RenumerationType { get; set; }
    public decimal CurrentManValue { get; set; }
    public decimal CurrentVHrRate { get; set; }
    public decimal ExpectedMHr { get; set; }
    public decimal ExpectedVHr
    {
        get
        {

            return ExpectedMHr * CurrentManValue;
        }
    }
    public decimal ExpectedRemuneration
    {
        get
        {

            return ExpectedVHr * CurrentVHrRate;
        }
    }
    public decimal VHrDifferencePercentage
    {
        get
        {

            return ExpectedVHr != 0 ? -(100.0m - (VHr / ExpectedVHr * 100.0m)) : 0;
        }
    }
    public WFTaskVHrDto EarnedInSelfPackages { get; set; }
    public WFTaskVHrDto RecordedInOthersPackages { get; set; }
    public WFTaskVHrDto RecordedForMeetings { get; set; }
    public WFTaskVHrDto RecordedForInspections { get; set; }
    public WFTaskVHrDto RecordedInProjectTodo { get; set; }
    public WFTaskVHrDto RecordedInTodo { get; set; }
    public WFTaskVHrDto RecordedForInquiries { get; set; }
    public WFTaskVHrDto RecordedForOtherTasks { get; set; }
    public decimal MHr
    {
        get
        {
            var _total = 0.0m;
            if (RecordedInOthersPackages != null) _total += RecordedInOthersPackages.MHrAssessed;
            if (RecordedForMeetings != null) _total += RecordedForMeetings.MHrAssessed;
            if (RecordedForInspections != null) _total += RecordedForInspections.MHrAssessed;
            if (RecordedInProjectTodo != null) _total += RecordedInProjectTodo.MHrAssessed;
            if (RecordedInTodo != null) _total += RecordedInTodo.MHrAssessed;
            if (RecordedForInquiries != null) _total += RecordedForInquiries.MHrAssessed;
            if (RecordedForOtherTasks != null) _total += RecordedForOtherTasks.MHrAssessed;
            if (EarnedInSelfPackages != null) _total += EarnedInSelfPackages.MHrAssessed;
            return Math.Round(_total, 2);
        }
    }
    public decimal VHr
    {
        get
        {
            var _total = 0.0m;
            if (RecordedInOthersPackages != null) _total += RecordedInOthersPackages.VHrAssessed;
            if (RecordedForMeetings != null) _total += RecordedForMeetings.VHrAssessed;
            if (RecordedForInspections != null) _total += RecordedForInspections.VHrAssessed;
            if (RecordedInProjectTodo != null) _total += RecordedInProjectTodo.VHrAssessed;
            if (RecordedInTodo != null) _total += RecordedInTodo.VHrAssessed;
            if (RecordedForInquiries != null) _total += RecordedForInquiries.VHrAssessed;
            if (RecordedForOtherTasks != null) _total += RecordedForOtherTasks.VHrAssessed;
            if (EarnedInSelfPackages != null) _total += EarnedInSelfPackages.VHrAssessed;
            return Math.Round(_total, 2);
        }
    }
    public decimal Remuneration
    {
        get
        {
            var _total = 0.0m;
            if (RecordedInOthersPackages != null) _total += RecordedInOthersPackages.VHrAssessedCost;
            if (RecordedForMeetings != null) _total += RecordedForMeetings.VHrAssessedCost;
            if (RecordedForInspections != null) _total += RecordedForInspections.VHrAssessedCost;
            if (RecordedInProjectTodo != null) _total += RecordedInProjectTodo.VHrAssessedCost;
            if (RecordedInTodo != null) _total += RecordedInTodo.VHrAssessedCost;
            if (RecordedForInquiries != null) _total += RecordedForInquiries.VHrAssessedCost;
            if (RecordedForOtherTasks != null) _total += RecordedForOtherTasks.VHrAssessedCost;
            if (EarnedInSelfPackages != null) _total += EarnedInSelfPackages.VHrAssessedCost;
            return Math.Round(_total, 2);
        }
    }
}

public class WFTaskRequest
{
    public DateTime Created { get; set; }
    public decimal OriginalMHr { get; set; }


    public decimal RequestedMHr { get; set; }


    public DateTime? OriginalDueDate { get; set; }


    public DateTime? RequestedDueDate { get; set; }

    public string Comment { get; set; }
    public string RequestMessage { get; set; }
    public string Status { get; set; }
}

public class WFTaskAnalysisAssessment
{

    public string Category { get; set; }

    public decimal Points { get; set; }

    public decimal ScoredPoints { get; set; }

    public string Comment { get; set; }

}

public class WFTaskAnalysisDto
{
    public string Person { get; set; }
    public string AssignedBy { get; set; }
    public string Entity { get; set; }
    public string EntityTitle { get; set; }
    public string TaskTitle { get; set; }
    public int Revision { get; set; }
    public string WorkDescription { get; set; }
    public string Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? CompletedDate { get; set; }
    public decimal Delay { get; set; }
    public string CommentOnCompletion { get; set; }
    public bool IsTimeBoundTask { get; set; }
    public decimal ManValue { get; set; }
    public decimal VHrRate { get; set; }
    public decimal MHrAssigned { get; set; }
    public decimal VHrAssigned { get; set; }
    public decimal VHrAssignedCost { get; set; }
    public decimal MHrConsumed { get; set; }
    public decimal VHrConsumed { get; set; }
    public decimal VHrConsumedCost { get; set; }
    public decimal MHrAssessed { get; set; }
    public decimal VHrAssessed { get; set; }
    public decimal VHrAssessedCost { get; set; }
    public bool IsAssessmentApplicable { get; set; }
    public decimal AssessmentPoints { get; set; }
    public string AssessmentRemark { get; set; }
    public string AssessmentSummary { get; set; }
    public int WFTaskID { get; set; }
    public int? EntityID { get; set; }
    public int ContactID { get; set; }

    public virtual IEnumerable<WFTaskAnalysisAssessment> Assessments { get; set; }
    public virtual IEnumerable<WFTaskRequest> Requests { get; set; }
}